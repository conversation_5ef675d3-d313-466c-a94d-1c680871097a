"""
Tool schemas for request/response validation.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel


class ProcessingRequest(BaseModel):
    """Base schema for processing requests."""
    session_id: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None


class ProcessingResponse(BaseModel):
    """Base schema for processing responses."""
    task_id: str
    status: str
    message: str
    session_id: str
    download_url: Optional[str] = None
    processing_time: Optional[float] = None
    created_at: datetime


class TaskStatus(BaseModel):
    """Schema for task status."""
    task_id: str
    status: str
    progress: Optional[float] = None
    message: Optional[str] = None
    error: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class FileInfo(BaseModel):
    """Schema for file information."""
    filename: str
    size: int
    mime_type: Optional[str] = None
    extension: str


class SessionInfo(BaseModel):
    """Schema for session information."""
    session_id: str
    created_at: datetime
    last_activity: datetime
    active_tasks: int


# Tool-specific schemas

class MergeRequest(ProcessingRequest):
    """Schema for PDF merge request."""
    pass  # Files are uploaded separately


class CompressRequest(ProcessingRequest):
    """Schema for PDF compression request."""
    compression_level: str = "medium"  # low, medium, high


class SplitRequest(ProcessingRequest):
    """Schema for PDF split request."""
    split_type: str = "pages"  # pages, range, size
    split_value: Optional[str] = None  # page numbers, ranges, or size


class ConversionRequest(ProcessingRequest):
    """Schema for format conversion requests."""
    output_format: Optional[str] = None
    quality: Optional[str] = "high"


class WatermarkRequest(ProcessingRequest):
    """Schema for watermark request."""
    watermark_text: str
    position: str = "center"  # center, top-left, top-right, bottom-left, bottom-right
    opacity: float = 0.5
    font_size: int = 12


class RotateRequest(ProcessingRequest):
    """Schema for rotation request."""
    angle: int = 90  # 90, 180, 270
    pages: Optional[str] = "all"  # all, odd, even, or specific pages


class ProtectRequest(ProcessingRequest):
    """Schema for PDF protection request."""
    password: str
    permissions: Optional[Dict[str, bool]] = None


class UnlockRequest(ProcessingRequest):
    """Schema for PDF unlock request."""
    password: str


class OCRRequest(ProcessingRequest):
    """Schema for OCR request."""
    language: str = "eng"
    output_format: str = "pdf"  # pdf, text


class PageNumbersRequest(ProcessingRequest):
    """Schema for page numbers request."""
    position: str = "bottom-center"
    start_number: int = 1
    format_style: str = "number"  # number, roman, letter
