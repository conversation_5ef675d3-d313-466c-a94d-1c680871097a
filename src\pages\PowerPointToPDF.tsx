import { useState } from 'react';
import { FileText, Download, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const PowerPointToPDF = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    convertPowerPointToPDF, resetState
  } = usePDFProcessor();
  
  const [files, setFiles] = useState<File[]>([]);
  const [quality, setQuality] = useState<'standard' | 'high'>('high');
  const [includeNotes, setIncludeNotes] = useState(false);
  const [slidesPerPage, setSlidesPerPage] = useState(1);
  const [preserveAnimations, setPreserveAnimations] = useState(false);
  const [preserveLinks, setPreserveLinks] = useState(true);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleConvert = async () => {
    if (files.length === 0) return;
    
    try {
      await convertPowerPointToPDF(files, {
        quality,
        include_notes: includeNotes,
        slides_per_page: slidesPerPage,
        preserve_animations: preserveAnimations,
        preserve_links: preserveLinks
      });
    } catch (err) {
      console.error('PowerPoint to PDF conversion failed:', err);
    }
  };
  
  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="PowerPoint en PDF"
      description="Facilitez la lecture de vos présentations PPT et PPTX en les convertissant en PDF"
      icon={<FileText className="w-8 h-8" />}
      color="from-rose-500 to-rose-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".ppt,.pptx"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre présentation PowerPoint"
          description="Glissez-déposez un fichier PPT ou PPTX ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de conversion
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Qualité
                </label>
                <select
                  value={quality}
                  onChange={(e) => setQuality(e.target.value as 'standard' | 'high')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500"
                >
                  <option value="standard">Standard (plus rapide)</option>
                  <option value="high">Haute qualité (meilleur rendu)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Diapositives par page
                </label>
                <select
                  value={slidesPerPage}
                  onChange={(e) => setSlidesPerPage(Number(e.target.value))}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500"
                >
                  <option value="1">1 diapositive par page</option>
                  <option value="2">2 diapositives par page</option>
                  <option value="4">4 diapositives par page</option>
                  <option value="6">6 diapositives par page</option>
                </select>
              </div>
            
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="preserve-animations"
                  checked={preserveAnimations}
                  onChange={(e) => setPreserveAnimations(e.target.checked)}
                  className="text-rose-600 rounded"
                />
                <label htmlFor="preserve-animations" className="text-slate-700">
                  Préserver les animations (notes dans le PDF)
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="include-notes"
                  checked={includeNotes}
                  onChange={(e) => setIncludeNotes(e.target.checked)}
                  className="text-rose-600 rounded"
                />
                <label htmlFor="include-notes" className="text-slate-700">
                  Inclure les notes du présentateur
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="preserve-links"
                  checked={preserveLinks}
                  onChange={(e) => setPreserveLinks(e.target.checked)}
                  className="text-rose-600 rounded"
                />
                <label htmlFor="preserve-links" className="text-slate-700">
                  Préserver les liens hypertextes
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-rose-50 border border-rose-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-rose-600"></div>
              <span className="text-rose-800 font-medium">Conversion en cours...</span>
            </div>
            <div className="w-full bg-rose-200 rounded-full h-2 mb-2">
              <div className="bg-rose-600 h-2 rounded-full transition-all duration-300"
                   style={{ width: `${progress}%` }}></div>
            </div>
            <p className="text-sm text-rose-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la conversion</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Conversion terminée avec succès!</h4>
                  <p className="text-sm text-green-700">{outputFiles.length} fichier(s) PDF généré(s)</p>
                </div>
              </div>
              <button 
                onClick={handleDownload}
                className="bg-rose-600 text-white px-4 py-2 rounded-lg hover:bg-rose-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-rose-600 to-pink-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default PowerPointToPDF;