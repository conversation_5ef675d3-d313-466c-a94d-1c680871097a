"""
PDF watermark tool for adding text or image watermarks to PDFs.
"""
import os
from typing import List, Dict, Any, Optional
from PyPDF2 import PdfWriter, PdfReader
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.colors import Color
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFWatermarkTool(BasePDFTool):
    """Tool for adding watermarks to PDF files."""
    
    def __init__(self):
        super().__init__("watermark")
        
        # Watermark positions
        self.positions = {
            "center": (0.5, 0.5),
            "top-left": (0.1, 0.9),
            "top-center": (0.5, 0.9),
            "top-right": (0.9, 0.9),
            "middle-left": (0.1, 0.5),
            "middle-right": (0.9, 0.5),
            "bottom-left": (0.1, 0.1),
            "bottom-center": (0.5, 0.1),
            "bottom-right": (0.9, 0.1)
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Add watermarks to PDF files.
        
        Args:
            input_files: List of PDF file paths to watermark
            output_path: Output directory for watermarked PDFs
            parameters: Watermark parameters (text, position, opacity, etc.)
            
        Returns:
            List containing paths to watermarked PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for watermarking")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        watermark_text = params.get("watermark_text", "WATERMARK")
        position = params.get("position", "center")
        opacity = params.get("opacity", 0.3)
        font_size = params.get("font_size", 48)
        color = params.get("color", "gray")
        rotation = params.get("rotation", 45)
        
        # Validate parameters
        if position not in self.positions:
            raise ValidationError(f"Invalid position: {position}. Available: {list(self.positions.keys())}")
        
        if not isinstance(opacity, (int, float)) or opacity < 0 or opacity > 1:
            raise ValidationError("Opacity must be between 0 and 1")
        
        if not isinstance(font_size, int) or font_size < 6 or font_size > 200:
            raise ValidationError("Font size must be between 6 and 200")
        
        if not isinstance(rotation, (int, float)) or rotation < -360 or rotation > 360:
            raise ValidationError("Rotation must be between -360 and 360 degrees")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF watermarking",
                input_count=len(input_files),
                watermark_text=watermark_text,
                position=position,
                opacity=opacity,
                font_size=font_size,
                rotation=rotation
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Watermarking file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}_watermarked.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Add watermark to the PDF
                    await self._add_watermark(
                        input_file, 
                        output_file, 
                        watermark_text,
                        position,
                        opacity,
                        font_size,
                        color,
                        rotation
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create watermarked PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} watermarked successfully",
                        input_file=input_file,
                        output_file=output_file,
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to watermark file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to watermark {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PDF watermarking completed successfully",
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF watermarking: {str(e)}")
    
    async def _add_watermark(
        self, 
        input_file: str, 
        output_file: str, 
        watermark_text: str,
        position: str,
        opacity: float,
        font_size: int,
        color: str,
        rotation: float
    ):
        """Add watermark to a single PDF file."""
        try:
            # Read the input PDF
            with open(input_file, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                # Check if PDF is encrypted
                if pdf_reader.is_encrypted:
                    raise ProcessingError("Cannot watermark encrypted PDF")
                
                # Create PDF writer
                pdf_writer = PdfWriter()
                
                # Process each page
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    
                    # Get page dimensions
                    page_width = float(page.mediabox.width)
                    page_height = float(page.mediabox.height)
                    
                    # Create watermark for this page
                    watermark_pdf = self._create_watermark_pdf(
                        watermark_text,
                        position,
                        opacity,
                        font_size,
                        color,
                        rotation,
                        page_width,
                        page_height
                    )
                    
                    # Read the watermark PDF
                    watermark_reader = PdfReader(watermark_pdf)
                    watermark_page = watermark_reader.pages[0]
                    
                    # Merge the watermark with the page
                    page.merge_page(watermark_page)
                    
                    # Add the page to the writer
                    pdf_writer.add_page(page)
                    
                    # Clean up temporary watermark file
                    os.remove(watermark_pdf)
                
                # Copy metadata if available
                if pdf_reader.metadata:
                    pdf_writer.add_metadata(pdf_reader.metadata)
                
                # Write the watermarked PDF
                with open(output_file, 'wb') as output_pdf:
                    pdf_writer.write(output_pdf)
                
                self.logger.debug(
                    "Watermark applied successfully",
                    input_file=input_file,
                    output_file=output_file,
                    pages=len(pdf_reader.pages)
                )
                
        except Exception as e:
            raise ProcessingError(f"Failed to add watermark: {str(e)}")
    
    def _create_watermark_pdf(
        self,
        text: str,
        position: str,
        opacity: float,
        font_size: int,
        color: str,
        rotation: float,
        page_width: float,
        page_height: float
    ) -> str:
        """Create a temporary PDF with the watermark."""
        temp_watermark_file = f"temp_watermark_{os.getpid()}.pdf"
        
        try:
            # Create canvas with the same size as the page
            c = canvas.Canvas(temp_watermark_file, pagesize=(page_width, page_height))
            
            # Set transparency
            c.setFillAlpha(opacity)
            
            # Set color
            watermark_color = self._get_color(color)
            c.setFillColor(watermark_color)
            
            # Set font
            c.setFont("Helvetica-Bold", font_size)
            
            # Calculate position
            pos_x, pos_y = self.positions[position]
            x = page_width * pos_x
            y = page_height * pos_y
            
            # Save graphics state
            c.saveState()
            
            # Move to position and rotate
            c.translate(x, y)
            c.rotate(rotation)
            
            # Draw text centered at the position
            text_width = c.stringWidth(text, "Helvetica-Bold", font_size)
            c.drawString(-text_width / 2, -font_size / 2, text)
            
            # Restore graphics state
            c.restoreState()
            
            # Save the PDF
            c.save()
            
            return temp_watermark_file
            
        except Exception as e:
            # Clean up if there was an error
            if os.path.exists(temp_watermark_file):
                os.remove(temp_watermark_file)
            raise ProcessingError(f"Failed to create watermark: {str(e)}")
    
    def _get_color(self, color_name: str) -> Color:
        """Convert color name to ReportLab Color object."""
        color_map = {
            "black": Color(0, 0, 0),
            "white": Color(1, 1, 1),
            "red": Color(1, 0, 0),
            "green": Color(0, 1, 0),
            "blue": Color(0, 0, 1),
            "yellow": Color(1, 1, 0),
            "cyan": Color(0, 1, 1),
            "magenta": Color(1, 0, 1),
            "gray": Color(0.5, 0.5, 0.5),
            "grey": Color(0.5, 0.5, 0.5),
            "lightgray": Color(0.8, 0.8, 0.8),
            "darkgray": Color(0.3, 0.3, 0.3)
        }
        
        return color_map.get(color_name.lower(), Color(0.5, 0.5, 0.5))
    
    def get_watermark_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available watermark options."""
        return {
            "watermark_text": {
                "description": "Text to use as watermark",
                "type": "string",
                "default": "WATERMARK",
                "example": "CONFIDENTIAL"
            },
            "position": {
                "description": "Position of the watermark on the page",
                "type": "string",
                "options": list(self.positions.keys()),
                "default": "center"
            },
            "opacity": {
                "description": "Transparency of the watermark (0.0 = transparent, 1.0 = opaque)",
                "type": "number",
                "min": 0.0,
                "max": 1.0,
                "default": 0.3
            },
            "font_size": {
                "description": "Font size of the watermark text",
                "type": "integer",
                "min": 6,
                "max": 200,
                "default": 48
            },
            "color": {
                "description": "Color of the watermark text",
                "type": "string",
                "options": ["black", "white", "red", "green", "blue", "yellow", "cyan", "magenta", "gray", "lightgray", "darkgray"],
                "default": "gray"
            },
            "rotation": {
                "description": "Rotation angle of the watermark in degrees",
                "type": "number",
                "min": -360,
                "max": 360,
                "default": 45
            }
        }
    
    def get_available_positions(self) -> List[str]:
        """Get list of available watermark positions."""
        return list(self.positions.keys())
    
    def get_available_colors(self) -> List[str]:
        """Get list of available colors."""
        return ["black", "white", "red", "green", "blue", "yellow", "cyan", "magenta", "gray", "lightgray", "darkgray"]
    
    async def preview_watermark_settings(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Preview watermark settings without applying to a PDF."""
        watermark_text = parameters.get("watermark_text", "WATERMARK")
        position = parameters.get("position", "center")
        opacity = parameters.get("opacity", 0.3)
        font_size = parameters.get("font_size", 48)
        color = parameters.get("color", "gray")
        rotation = parameters.get("rotation", 45)
        
        # Validate parameters
        validation_errors = []
        
        if position not in self.positions:
            validation_errors.append(f"Invalid position: {position}")
        
        if not isinstance(opacity, (int, float)) or opacity < 0 or opacity > 1:
            validation_errors.append("Opacity must be between 0 and 1")
        
        if not isinstance(font_size, int) or font_size < 6 or font_size > 200:
            validation_errors.append("Font size must be between 6 and 200")
        
        if not isinstance(rotation, (int, float)) or rotation < -360 or rotation > 360:
            validation_errors.append("Rotation must be between -360 and 360 degrees")
        
        return {
            "watermark_text": watermark_text,
            "position": position,
            "position_coordinates": self.positions.get(position, (0.5, 0.5)),
            "opacity": opacity,
            "font_size": font_size,
            "color": color,
            "rotation": rotation,
            "validation_errors": validation_errors,
            "is_valid": len(validation_errors) == 0
        }


# Create tool instance
watermark_pdf_tool = PDFWatermarkTool()
