"""
PDF unlock tool for removing password protection from PDFs.
"""
import os
from typing import List, Dict, Any, Optional
from PyPDF2 import Pdf<PERSON>rite<PERSON>, PdfReader
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFUnlockTool(BasePDFTool):
    """Tool for removing password protection from PDF files."""
    
    def __init__(self):
        super().__init__("unlock")
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Remove password protection from PDF files.
        
        Args:
            input_files: List of PDF file paths to unlock
            output_path: Output directory for unlocked PDFs
            parameters: Unlock parameters (password, etc.)
            
        Returns:
            List containing paths to unlocked PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for unlocking")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        password = params.get("password", "")
        
        if not password:
            raise ValidationError("Password is required to unlock encrypted PDFs")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF unlock",
                input_count=len(input_files),
                has_password=bool(password)
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Unlocking file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}_unlocked.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Unlock the PDF
                    await self._unlock_pdf(input_file, output_file, password)
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create unlocked PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} unlocked successfully",
                        input_file=input_file,
                        output_file=output_file,
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to unlock file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to unlock {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PDF unlock completed successfully",
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF unlock: {str(e)}")
    
    async def _unlock_pdf(self, input_file: str, output_file: str, password: str):
        """Unlock a single PDF file by removing password protection."""
        try:
            # Read the input PDF
            with open(input_file, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                # Check if PDF is encrypted
                if not pdf_reader.is_encrypted:
                    self.logger.warning("PDF is not encrypted", file_path=input_file)
                    # Just copy the file since it's not encrypted
                    import shutil
                    shutil.copy2(input_file, output_file)
                    return
                
                # Try to decrypt with the provided password
                try:
                    decrypt_result = pdf_reader.decrypt(password)
                    if decrypt_result == 0:
                        raise ProcessingError("Incorrect password provided")
                    elif decrypt_result == 1:
                        self.logger.info("PDF decrypted with user password")
                    elif decrypt_result == 2:
                        self.logger.info("PDF decrypted with owner password")
                except Exception as e:
                    raise ProcessingError(f"Failed to decrypt PDF: {str(e)}")
                
                # Create PDF writer
                pdf_writer = PdfWriter()
                
                # Copy all pages
                for page_num in range(len(pdf_reader.pages)):
                    pdf_writer.add_page(pdf_reader.pages[page_num])
                
                # Copy metadata if available
                if pdf_reader.metadata:
                    pdf_writer.add_metadata(pdf_reader.metadata)
                
                # Write the unlocked PDF (without encryption)
                with open(output_file, 'wb') as output_pdf:
                    pdf_writer.write(output_pdf)
                
                self.logger.debug(
                    "PDF decryption completed",
                    input_file=input_file,
                    output_file=output_file
                )
                
        except Exception as e:
            raise ProcessingError(f"Failed to unlock PDF: {str(e)}")
    
    async def test_password(self, file_path: str, password: str) -> Dict[str, Any]:
        """Test if a password can unlock a PDF file without actually unlocking it."""
        try:
            with open(file_path, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                if not pdf_reader.is_encrypted:
                    return {
                        "filename": os.path.basename(file_path),
                        "is_encrypted": False,
                        "password_valid": None,
                        "message": "PDF is not password protected"
                    }
                
                # Try to decrypt with the password
                try:
                    decrypt_result = pdf_reader.decrypt(password)
                    
                    if decrypt_result == 0:
                        return {
                            "filename": os.path.basename(file_path),
                            "is_encrypted": True,
                            "password_valid": False,
                            "message": "Incorrect password"
                        }
                    elif decrypt_result == 1:
                        return {
                            "filename": os.path.basename(file_path),
                            "is_encrypted": True,
                            "password_valid": True,
                            "password_type": "user",
                            "message": "Password is valid (user password)"
                        }
                    elif decrypt_result == 2:
                        return {
                            "filename": os.path.basename(file_path),
                            "is_encrypted": True,
                            "password_valid": True,
                            "password_type": "owner",
                            "message": "Password is valid (owner password)"
                        }
                    else:
                        return {
                            "filename": os.path.basename(file_path),
                            "is_encrypted": True,
                            "password_valid": False,
                            "message": "Unknown decryption result"
                        }
                
                except Exception as e:
                    return {
                        "filename": os.path.basename(file_path),
                        "is_encrypted": True,
                        "password_valid": False,
                        "message": f"Error testing password: {str(e)}"
                    }
                
        except Exception as e:
            self.logger.error("Failed to test password", file_path=file_path, error=str(e))
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }
    
    async def get_pdf_encryption_info(self, file_path: str) -> Dict[str, Any]:
        """Get detailed information about PDF encryption."""
        try:
            with open(file_path, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                info = {
                    "filename": os.path.basename(file_path),
                    "is_encrypted": pdf_reader.is_encrypted,
                    "page_count": len(pdf_reader.pages),
                    "size_mb": round(self.get_file_size_mb(file_path), 2)
                }
                
                if pdf_reader.is_encrypted:
                    info["encryption_note"] = "Document is password protected and requires a password to unlock"
                    info["unlock_required"] = True
                else:
                    info["encryption_note"] = "Document is not password protected"
                    info["unlock_required"] = False
                
                # Try to get metadata (may not be accessible if encrypted)
                try:
                    if pdf_reader.metadata:
                        info["title"] = pdf_reader.metadata.get("/Title", "")
                        info["author"] = pdf_reader.metadata.get("/Author", "")
                        info["creator"] = pdf_reader.metadata.get("/Creator", "")
                except Exception:
                    if pdf_reader.is_encrypted:
                        info["metadata_note"] = "Metadata not accessible due to encryption"
                
                return info
                
        except Exception as e:
            self.logger.error("Failed to get PDF encryption info", file_path=file_path, error=str(e))
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }
    
    def get_unlock_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available unlock options."""
        return {
            "password": {
                "description": "Password to unlock the PDF",
                "type": "string",
                "required": True,
                "note": "This should be the user password or owner password for the PDF"
            }
        }
    
    async def try_common_passwords(self, file_path: str, common_passwords: List[str] = None) -> Dict[str, Any]:
        """Try a list of common passwords to unlock a PDF (for testing purposes)."""
        if common_passwords is None:
            # Default list of common passwords (for demonstration only)
            common_passwords = [
                "", "password", "123456", "admin", "user", "test", "pdf", "document"
            ]
        
        try:
            with open(file_path, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                if not pdf_reader.is_encrypted:
                    return {
                        "filename": os.path.basename(file_path),
                        "is_encrypted": False,
                        "message": "PDF is not password protected"
                    }
                
                for password in common_passwords:
                    try:
                        decrypt_result = pdf_reader.decrypt(password)
                        if decrypt_result > 0:
                            return {
                                "filename": os.path.basename(file_path),
                                "is_encrypted": True,
                                "password_found": True,
                                "password": password,
                                "password_type": "user" if decrypt_result == 1 else "owner",
                                "message": f"Password found: '{password}'"
                            }
                    except Exception:
                        continue
                
                return {
                    "filename": os.path.basename(file_path),
                    "is_encrypted": True,
                    "password_found": False,
                    "passwords_tried": len(common_passwords),
                    "message": "No common password worked"
                }
                
        except Exception as e:
            self.logger.error("Failed to try common passwords", file_path=file_path, error=str(e))
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }


# Create tool instance
unlock_pdf_tool = PDFUnlockTool()
