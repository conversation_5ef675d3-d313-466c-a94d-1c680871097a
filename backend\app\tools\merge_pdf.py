"""
PDF merge tool for combining multiple PDF files into one.
"""
import os
from typing import List, Dict, Any, Optional
from PyPDF2 import PdfWriter, PdfReader
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFMergeTool(BasePDFTool):
    """Tool for merging multiple PDF files into a single PDF."""
    
    def __init__(self):
        super().__init__("merge")
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Merge multiple PDF files into one.
        
        Args:
            input_files: List of PDF file paths to merge
            output_path: Output directory or file path
            parameters: Optional parameters (order, bookmarks, etc.)
            
        Returns:
            List containing the path to the merged PDF file
        """
        # Validate inputs
        if len(input_files) < 2:
            raise ValidationError("At least 2 PDF files are required for merging")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        preserve_bookmarks = params.get("preserve_bookmarks", True)
        file_order = params.get("file_order", list(range(len(input_files))))
        
        # Ensure output directory exists
        if os.path.isdir(output_path):
            output_file = os.path.join(output_path, "merged.pdf")
        else:
            output_file = output_path
            self.ensure_output_directory(os.path.dirname(output_file))
        
        try:
            # Create PDF writer
            pdf_writer = PdfWriter()
            
            # Process files in specified order
            ordered_files = [input_files[i] for i in file_order if i < len(input_files)]
            
            self.logger.info(
                "Starting PDF merge",
                input_count=len(ordered_files),
                output_file=output_file,
                preserve_bookmarks=preserve_bookmarks
            )
            
            for i, file_path in enumerate(ordered_files):
                try:
                    self.logger.debug(f"Processing file {i+1}/{len(ordered_files)}", file_path=file_path)
                    
                    # Read PDF file
                    with open(file_path, 'rb') as pdf_file:
                        pdf_reader = PdfReader(pdf_file)
                        
                        # Check if PDF is encrypted
                        if pdf_reader.is_encrypted:
                            self.logger.warning("Encrypted PDF detected", file_path=file_path)
                            raise ProcessingError(f"Cannot merge encrypted PDF: {os.path.basename(file_path)}")
                        
                        # Add all pages from this PDF
                        page_count = len(pdf_reader.pages)
                        for page_num in range(page_count):
                            page = pdf_reader.pages[page_num]
                            pdf_writer.add_page(page)
                        
                        # Add bookmarks if requested
                        if preserve_bookmarks and pdf_reader.outline:
                            try:
                                # Add bookmark for this file
                                bookmark_title = os.path.splitext(os.path.basename(file_path))[0]
                                pdf_writer.add_outline_item(bookmark_title, len(pdf_writer.pages) - page_count)
                            except Exception as e:
                                self.logger.warning("Failed to add bookmark", file_path=file_path, error=str(e))
                        
                        self.logger.debug(
                            f"Added {page_count} pages from file {i+1}",
                            file_path=file_path,
                            total_pages=len(pdf_writer.pages)
                        )
                
                except Exception as e:
                    self.logger.error(f"Failed to process file {i+1}", file_path=file_path, error=str(e))
                    raise ProcessingError(f"Failed to process {os.path.basename(file_path)}: {str(e)}")
            
            # Write merged PDF
            with open(output_file, 'wb') as output_pdf:
                pdf_writer.write(output_pdf)
            
            # Verify output file was created
            if not os.path.exists(output_file):
                raise ProcessingError("Failed to create merged PDF file")
            
            output_size = self.get_file_size_mb(output_file)
            total_input_size = sum(self.get_file_size_mb(f) for f in ordered_files)
            
            self.logger.info(
                "PDF merge completed successfully",
                output_file=output_file,
                total_pages=len(pdf_writer.pages),
                input_size_mb=round(total_input_size, 2),
                output_size_mb=round(output_size, 2)
            )
            
            return [output_file]
            
        except Exception as e:
            # Clean up output file if it was partially created
            if os.path.exists(output_file):
                try:
                    os.remove(output_file)
                except Exception:
                    pass
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF merge: {str(e)}")
    
    def validate_merge_parameters(self, parameters: Dict[str, Any], file_count: int) -> Dict[str, Any]:
        """Validate and normalize merge parameters."""
        validated = {}
        
        # Validate file order
        file_order = parameters.get("file_order", list(range(file_count)))
        if not isinstance(file_order, list):
            raise ValidationError("file_order must be a list of integers")
        
        if len(file_order) != file_count:
            raise ValidationError(f"file_order must contain exactly {file_count} indices")
        
        if not all(isinstance(i, int) and 0 <= i < file_count for i in file_order):
            raise ValidationError(f"file_order indices must be integers between 0 and {file_count-1}")
        
        if len(set(file_order)) != len(file_order):
            raise ValidationError("file_order cannot contain duplicate indices")
        
        validated["file_order"] = file_order
        
        # Validate bookmark preservation
        preserve_bookmarks = parameters.get("preserve_bookmarks", True)
        if not isinstance(preserve_bookmarks, bool):
            raise ValidationError("preserve_bookmarks must be a boolean")
        
        validated["preserve_bookmarks"] = preserve_bookmarks
        
        return validated
    
    async def get_pdf_info(self, file_path: str) -> Dict[str, Any]:
        """Get information about a PDF file."""
        try:
            with open(file_path, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                info = {
                    "filename": os.path.basename(file_path),
                    "page_count": len(pdf_reader.pages),
                    "is_encrypted": pdf_reader.is_encrypted,
                    "size_mb": round(self.get_file_size_mb(file_path), 2),
                    "has_bookmarks": bool(pdf_reader.outline)
                }
                
                # Try to get metadata
                try:
                    metadata = pdf_reader.metadata
                    if metadata:
                        info["title"] = metadata.get("/Title", "")
                        info["author"] = metadata.get("/Author", "")
                        info["creator"] = metadata.get("/Creator", "")
                        info["producer"] = metadata.get("/Producer", "")
                except Exception:
                    pass
                
                return info
                
        except Exception as e:
            self.logger.error("Failed to get PDF info", file_path=file_path, error=str(e))
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }


# Create tool instance
merge_tool = PDFMergeTool()
