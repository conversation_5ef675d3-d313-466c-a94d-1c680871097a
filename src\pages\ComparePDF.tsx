import { useState } from 'react';
import { Eye, ArrowRight, CheckCircle, AlertCircle, Download } from 'lucide-react';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';


const ComparePDF = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    comparePDF, resetState
  } = usePDFProcessor();
  
  const [originalFile, setOriginalFile] = useState<File[]>([]);
  const [modifiedFile, setModifiedFile] = useState<File[]>([]);
  const [comparisonType, setComparisonType] = useState<'text' | 'visual' | 'both'>('both');
  const [sensitivity, setSensitivity] = useState<'low' | 'medium' | 'high'>('medium');
  const [highlightColor, setHighlightColor] = useState<'red' | 'yellow' | 'green'>('yellow');
  const [generateReport, setGenerateReport] = useState(false);
  const [highlightChanges, setHighlightChanges] = useState(true);
  const [showDeletions, setShowDeletions] = useState(true);
  const [showAdditions, setShowAdditions] = useState(true);

  const handleOriginalFileSelect = (selectedFiles: File[]) => {
    setOriginalFile(selectedFiles);
    resetState();
  };

  const handleModifiedFileSelect = (selectedFiles: File[]) => {
    setModifiedFile(selectedFiles);
    resetState();
  };

  const handleCompare = async () => {
    if (originalFile.length === 0 || modifiedFile.length === 0) return;
    
    try {
      // Combine both files for comparison
      const allFiles = [...originalFile, ...modifiedFile];
      
      await comparePDF(allFiles, {
        method: comparisonType,
        sensitivity,
        highlight_color: highlightColor,
        generate_report: generateReport,
        highlight_changes: highlightChanges,
        show_deletions: showDeletions,
        show_additions: showAdditions
      });
    } catch (err) {
      console.error('PDF comparison failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Comparer PDF"
      description="Permet de comparer des documents côte à côte, en mettant facilement en évidence les changements entre les différentes versions"
      icon={<Eye className="w-8 h-8" />}
      color="from-green-500 to-green-600"
    >
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Document original
            </h3>
            <FileUpload
              onFileSelect={handleOriginalFileSelect}
              accept=".pdf"
              multiple={false}
              maxFiles={1}
              title="Fichier original"
              description="Glissez-déposez le document original ici"
            />
          </div>

          <div>
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Document modifié
            </h3>
            <FileUpload
              onFileSelect={handleModifiedFileSelect}
              accept=".pdf"
              multiple={false}
              maxFiles={1}
              title="Fichier modifié"
              description="Glissez-déposez le document modifié ici"
            />
          </div>
        </div>

        {originalFile.length > 0 && modifiedFile.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de comparaison
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Type de comparaison
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="comparisonType"
                      value="text"
                      checked={comparisonType === 'text'}
                      onChange={(e) => setComparisonType(e.target.value as 'text')}
                      className="text-green-600"
                    />
                    <span className="text-slate-700">Comparaison textuelle uniquement</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="comparisonType"
                      value="visual"
                      checked={comparisonType === 'visual'}
                      onChange={(e) => setComparisonType(e.target.value as 'visual')}
                      className="text-green-600"
                    />
                    <span className="text-slate-700">Comparaison visuelle uniquement</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="comparisonType"
                      value="both"
                      checked={comparisonType === 'both'}
                      onChange={(e) => setComparisonType(e.target.value as 'both')}
                      className="text-green-600"
                    />
                    <span className="text-slate-700">Comparaison complète (recommandé)</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Sensibilité de détection
                </label>
                <select
                  value={sensitivity}
                  onChange={(e) => setSensitivity(e.target.value as 'low' | 'medium' | 'high')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="low">Faible - Détecte les changements majeurs</option>
                  <option value="medium">Moyenne - Équilibre précision/détection</option>
                  <option value="high">Élevée - Détecte tous les changements</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Couleur de surlignage
                </label>
                <select
                  value={highlightColor}
                  onChange={(e) => setHighlightColor(e.target.value as 'red' | 'yellow' | 'green')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="yellow">Jaune</option>
                  <option value="red">Rouge</option>
                  <option value="green">Vert</option>
                </select>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="highlight-changes"
                    checked={highlightChanges}
                    onChange={(e) => setHighlightChanges(e.target.checked)}
                    className="text-green-600 rounded"
                  />
                  <label htmlFor="highlight-changes" className="text-slate-700">
                    Surligner les modifications
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="show-deletions"
                    checked={showDeletions}
                    onChange={(e) => setShowDeletions(e.target.checked)}
                    className="text-green-600 rounded"
                  />
                  <label htmlFor="show-deletions" className="text-slate-700">
                    Afficher les suppressions
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="show-additions"
                    checked={showAdditions}
                    onChange={(e) => setShowAdditions(e.target.checked)}
                    className="text-green-600 rounded"
                  />
                  <label htmlFor="show-additions" className="text-slate-700">
                    Afficher les ajouts
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="generate-report"
                    checked={generateReport}
                    onChange={(e) => setGenerateReport(e.target.checked)}
                    className="text-green-600 rounded"
                  />
                  <label htmlFor="generate-report" className="text-slate-700">
                    Générer un rapport de comparaison
                  </label>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-blue-800 font-medium">Types de changements détectés</span>
                </div>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  <li>• Ajouts de texte (surlignés en vert)</li>
                  <li>• Suppressions de texte (surlignés en rouge)</li>
                  <li>• Modifications de formatage</li>
                  <li>• Changements d'images</li>
                  <li>• Réorganisation de contenu</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-green-600"></div>
              <span className="text-green-800 font-medium">Comparaison en cours...</span>
            </div>
            <div className="w-full bg-green-200 rounded-full h-2 mb-2">
              <div className="bg-green-600 h-2 rounded-full transition-all duration-300"
                   style={{ width: `${progress}%` }}></div>
            </div>
            <p className="text-sm text-green-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la comparaison</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Comparaison terminée avec succès!</h4>
                  <p className="text-sm text-green-700">{outputFiles.length} fichier(s) de résultat généré(s)</p>
                </div>
              </div>
              <button 
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {originalFile.length > 0 && modifiedFile.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleCompare}
              disabled={isProcessing}
              className="bg-gradient-to-r from-green-600 to-teal-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Comparaison en cours...</span>
                </>
              ) : (
                <>
                  <span>Comparer les documents</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default ComparePDF;