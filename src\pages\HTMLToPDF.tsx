import { useState } from 'react';
import { Globe, Download, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const HTMLToPDF = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    convertHTMLToPDF, resetState
  } = usePDFProcessor();
  
  const [url, setUrl] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const [mode, setMode] = useState<'url' | 'upload'>('url');
  const [pageSize, setPageSize] = useState<'A4' | 'Letter' | 'Legal'>('A4');
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');
  const [margins, setMargins] = useState<'default' | 'minimal' | 'none'>('default');
  const [includeBackground, setIncludeBackground] = useState(true);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleConvert = async () => {
    if (mode === 'url' && !url) return;
    if (mode === 'upload' && files.length === 0) return;
    
    try {
      let filesToProcess = files;
      if (mode === 'url') {
        // For URL mode, we'll pass an empty file array and include the URL in parameters
        filesToProcess = [];
      }
      
      await convertHTMLToPDF(filesToProcess, {
        url: mode === 'url' ? url : undefined,
        page_size: pageSize,
        orientation,
        margins,
        include_background: includeBackground
      });
    } catch (err) {
      console.error('HTML to PDF conversion failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="HTML en PDF"
      description="Convertissez des pages web HTML en PDF. Copiez-collez l'URL de la page qui vous intéresse"
      icon={<Globe className="w-8 h-8" />}
      color="from-slate-500 to-slate-600"
    >
      <div className="space-y-6">
        {/* Mode Selection */}
        <div className="bg-slate-50 p-4 rounded-xl">
          <div className="flex space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="mode"
                value="url"
                checked={mode === 'url'}
                onChange={(e) => setMode(e.target.value as 'url')}
                className="text-slate-600"
              />
              <span className="text-slate-700 font-medium">URL de page web</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="mode"
                value="upload"
                checked={mode === 'upload'}
                onChange={(e) => setMode(e.target.value as 'upload')}
                className="text-slate-600"
              />
              <span className="text-slate-700 font-medium">Fichier HTML</span>
            </label>
          </div>
        </div>

        {mode === 'url' ? (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                URL de la page web
              </label>
              <input
                type="url"
                placeholder="https://example.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="w-full p-4 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 text-lg"
              />
            </div>
          </div>
        ) : (
          <FileUpload
            onFileSelect={handleFileSelect}
            accept=".html,.htm"
            multiple={false}
            maxFiles={1}
            title="Sélectionnez votre fichier HTML"
            description="Glissez-déposez un fichier HTML ici ou cliquez pour sélectionner"
          />
        )}

        {((mode === 'url' && url) || (mode === 'upload' && files.length > 0)) && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de conversion
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Format de page
                </label>
                <select
                  value={pageSize}
                  onChange={(e) => setPageSize(e.target.value as 'A4' | 'Letter' | 'Legal')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500"
                >
                  <option value="A4">A4</option>
                  <option value="Letter">Letter</option>
                  <option value="Legal">Legal</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Orientation
                </label>
                <select
                  value={orientation}
                  onChange={(e) => setOrientation(e.target.value as 'portrait' | 'landscape')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500"
                >
                  <option value="portrait">Portrait</option>
                  <option value="landscape">Paysage</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Marges
                </label>
                <select
                  value={margins}
                  onChange={(e) => setMargins(e.target.value as 'default' | 'minimal' | 'none')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500"
                >
                  <option value="default">Par défaut</option>
                  <option value="minimal">Minimales</option>
                  <option value="none">Aucune</option>
                </select>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="include-background"
                  checked={includeBackground}
                  onChange={(e) => setIncludeBackground(e.target.checked)}
                  className="text-slate-600 rounded"
                />
                <label htmlFor="include-background" className="text-slate-700">
                  Inclure les arrière-plans
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-slate-50 border border-slate-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-slate-600"></div>
              <span className="text-slate-800 font-medium">Conversion en cours...</span>
            </div>
            <div className="w-full bg-slate-200 rounded-full h-2 mb-2">
              <div className="bg-slate-600 h-2 rounded-full transition-all duration-300"
                   style={{ width: `${progress}%` }}></div>
            </div>
            <p className="text-sm text-slate-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la conversion</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Conversion terminée avec succès!</h4>
                  <p className="text-sm text-green-700">{outputFiles.length} fichier(s) PDF généré(s)</p>
                </div>
              </div>
              <button 
                onClick={handleDownload}
                className="bg-slate-600 text-white px-4 py-2 rounded-lg hover:bg-slate-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {((mode === 'url' && url) || (mode === 'upload' && files.length > 0)) && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-slate-600 to-gray-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default HTMLToPDF;