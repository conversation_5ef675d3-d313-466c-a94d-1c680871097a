"""
File management for secure upload, processing, and download.
"""
import os
import uuid
import aiofiles
import mimetypes
from typing import List, Optional, Dict, Any
from pathlib import Path
import structlog
from fastapi import UploadFile, HTTPException

from ..config import settings

logger = structlog.get_logger()


class FileManager:
    """Manages file uploads, validation, and temporary storage."""
    
    def __init__(self):
        self.allowed_mime_types = {
            ".pdf": ["application/pdf"],
            ".docx": ["application/vnd.openxmlformats-officedocument.wordprocessingml.document"],
            ".xlsx": ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],
            ".pptx": ["application/vnd.openxmlformats-officedocument.presentationml.presentation"],
            ".jpg": ["image/jpeg"],
            ".jpeg": ["image/jpeg"],
            ".png": ["image/png"],
            ".txt": ["text/plain"],
            ".html": ["text/html"]
        }
    
    def validate_file(self, file: UploadFile) -> bool:
        """Validate uploaded file."""
        # Check file size
        if hasattr(file, 'size') and file.size > settings.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"File size exceeds maximum allowed size of {settings.max_file_size} bytes"
            )
        
        # Check file extension
        if file.filename:
            file_ext = Path(file.filename).suffix.lower()
            if file_ext not in settings.allowed_extensions:
                raise HTTPException(
                    status_code=400,
                    detail=f"File type {file_ext} not allowed. Allowed types: {settings.allowed_extensions}"
                )
            
            # Check MIME type
            if file.content_type:
                allowed_mimes = self.allowed_mime_types.get(file_ext, [])
                if allowed_mimes and file.content_type not in allowed_mimes:
                    logger.warning(
                        "MIME type mismatch",
                        filename=file.filename,
                        expected=allowed_mimes,
                        actual=file.content_type
                    )
                    # Don't reject, just log warning as browsers can be inconsistent
        
        return True
    
    async def save_upload_file(self, file: UploadFile, session_dir: str, subfolder: str = "uploads") -> str:
        """Save uploaded file to session directory."""
        self.validate_file(file)
        
        # Generate unique filename
        file_ext = Path(file.filename).suffix.lower() if file.filename else ""
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        
        # Create full path
        upload_dir = os.path.join(session_dir, subfolder)
        os.makedirs(upload_dir, exist_ok=True)
        file_path = os.path.join(upload_dir, unique_filename)
        
        # Save file
        try:
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)
            
            # Get actual file size
            file_size = os.path.getsize(file_path)
            
            logger.info(
                "File uploaded successfully",
                original_filename=file.filename,
                saved_path=file_path,
                size=file_size
            )
            
            return file_path
            
        except Exception as e:
            logger.error("Failed to save uploaded file", filename=file.filename, error=str(e))
            # Clean up partial file
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(status_code=500, detail="Failed to save uploaded file")
    
    async def save_multiple_files(self, files: List[UploadFile], session_dir: str, subfolder: str = "uploads") -> List[str]:
        """Save multiple uploaded files."""
        saved_files = []
        
        try:
            for file in files:
                file_path = await self.save_upload_file(file, session_dir, subfolder)
                saved_files.append(file_path)
            
            return saved_files
            
        except Exception as e:
            # Clean up any files that were saved before the error
            for file_path in saved_files:
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except Exception as cleanup_error:
                        logger.error("Failed to cleanup file", file_path=file_path, error=str(cleanup_error))
            raise e
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get information about a file."""
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="File not found")
        
        stat = os.stat(file_path)
        file_ext = Path(file_path).suffix.lower()
        mime_type, _ = mimetypes.guess_type(file_path)
        
        return {
            "path": file_path,
            "filename": os.path.basename(file_path),
            "size": stat.st_size,
            "extension": file_ext,
            "mime_type": mime_type,
            "created_at": stat.st_ctime,
            "modified_at": stat.st_mtime
        }
    
    def create_output_path(self, session_dir: str, filename: str, subfolder: str = "outputs") -> str:
        """Create a path for output file."""
        output_dir = os.path.join(session_dir, subfolder)
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate unique filename if file already exists
        base_name = Path(filename).stem
        file_ext = Path(filename).suffix
        counter = 1
        
        output_path = os.path.join(output_dir, filename)
        while os.path.exists(output_path):
            new_filename = f"{base_name}_{counter}{file_ext}"
            output_path = os.path.join(output_dir, new_filename)
            counter += 1
        
        return output_path
    
    def cleanup_files(self, file_paths: List[str]):
        """Clean up a list of files."""
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.debug("Cleaned up file", file_path=file_path)
            except Exception as e:
                logger.error("Failed to cleanup file", file_path=file_path, error=str(e))
    
    def get_file_size_mb(self, file_path: str) -> float:
        """Get file size in MB."""
        if os.path.exists(file_path):
            return os.path.getsize(file_path) / (1024 * 1024)
        return 0.0
    
    def is_pdf_file(self, file_path: str) -> bool:
        """Check if file is a PDF."""
        return Path(file_path).suffix.lower() == ".pdf"
    
    def is_image_file(self, file_path: str) -> bool:
        """Check if file is an image."""
        return Path(file_path).suffix.lower() in [".jpg", ".jpeg", ".png"]
    
    def is_document_file(self, file_path: str) -> bool:
        """Check if file is a document."""
        return Path(file_path).suffix.lower() in [".docx", ".xlsx", ".pptx"]
    
    async def read_file_content(self, file_path: str) -> bytes:
        """Read file content asynchronously."""
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="File not found")
        
        try:
            async with aiofiles.open(file_path, 'rb') as f:
                return await f.read()
        except Exception as e:
            logger.error("Failed to read file", file_path=file_path, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to read file")
    
    def get_download_filename(self, original_filename: str, tool_name: str) -> str:
        """Generate a download filename based on the tool used."""
        base_name = Path(original_filename).stem
        
        # Tool-specific filename patterns
        if tool_name == "merge":
            return f"{base_name}_merged.pdf"
        elif tool_name == "compress":
            return f"{base_name}_compressed.pdf"
        elif tool_name == "split":
            return f"{base_name}_split.pdf"
        elif tool_name == "pdf_to_word":
            return f"{base_name}.docx"
        elif tool_name == "pdf_to_jpg":
            return f"{base_name}.jpg"
        elif tool_name == "jpg_to_pdf":
            return f"{base_name}.pdf"
        elif tool_name == "watermark":
            return f"{base_name}_watermarked.pdf"
        elif tool_name == "rotate":
            return f"{base_name}_rotated.pdf"
        elif tool_name == "protect":
            return f"{base_name}_protected.pdf"
        elif tool_name == "unlock":
            return f"{base_name}_unlocked.pdf"
        else:
            return f"{base_name}_processed.pdf"
