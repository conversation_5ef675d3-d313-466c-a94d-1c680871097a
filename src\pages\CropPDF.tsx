import { useState } from 'react';
import { Crop, Download, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const CropPDF = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    cropPDF, resetState
  } = usePDFProcessor();
  
  const [files, setFiles] = useState<File[]>([]);
  const [cropMode, setCropMode] = useState<'auto' | 'margins' | 'preset' | 'custom'>('auto');
  const [marginThreshold, setMarginThreshold] = useState(10);
  const [pages, setPages] = useState<string>('');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleCrop = async () => {
    if (files.length === 0) return;

    try {
      await cropPDF(files, {
        crop_mode: cropMode,
        margin_threshold: marginThreshold,
        pages: pages || undefined
      });
    } catch (err) {
      console.error('PDF cropping failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Rogner PDF"
      description="Réduisez les marges de vos documents PDF ou sélectionnez une zone à rogner"
      icon={<Crop className="w-8 h-8" />}
      color="from-orange-500 to-orange-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de rognage
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Mode de rognage
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="cropMode"
                      value="margins"
                      checked={cropMode === 'margins'}
                      onChange={(e) => setCropMode(e.target.value as 'margins')}
                      className="text-orange-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Réduire les marges</span>
                      <p className="text-sm text-slate-500">Supprime automatiquement les marges excessives</p>
                    </div>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="cropMode"
                      value="custom"
                      checked={cropMode === 'custom'}
                      onChange={(e) => setCropMode(e.target.value as 'custom')}
                      className="text-orange-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Sélection personnalisée</span>
                      <p className="text-sm text-slate-500">Sélectionnez manuellement la zone à conserver</p>
                    </div>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="cropMode"
                      value="preset"
                      checked={cropMode === 'preset'}
                      onChange={(e) => setCropMode(e.target.value as 'preset')}
                      className="text-orange-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Formats prédéfinis</span>
                      <p className="text-sm text-slate-500">Utilise des formats standard (A4, Letter, etc.)</p>
                    </div>
                  </label>
                </div>
              </div>

              {cropMode === 'preset' && (
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Format de page
                  </label>
                  <select className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                    <option value="a4">A4 (210 × 297 mm)</option>
                    <option value="letter">Letter (8.5 × 11 in)</option>
                    <option value="legal">Legal (8.5 × 14 in)</option>
                    <option value="a3">A3 (297 × 420 mm)</option>
                    <option value="tabloid">Tabloid (11 × 17 in)</option>
                  </select>
                </div>
              )}

              {cropMode === 'margins' && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Marge supérieure (mm)
                    </label>
                    <input
                      type="number"
                      defaultValue="10"
                      className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Marge inférieure (mm)
                    </label>
                    <input
                      type="number"
                      defaultValue="10"
                      className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Marge gauche (mm)
                    </label>
                    <input
                      type="number"
                      defaultValue="10"
                      className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Marge droite (mm)
                    </label>
                    <input
                      type="number"
                      defaultValue="10"
                      className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>
                </div>
              )}

              {cropMode === 'auto' && (
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Seuil de marge automatique: {marginThreshold}mm
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="50"
                    value={marginThreshold}
                    onChange={(e) => setMarginThreshold(parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>1mm (Serré)</span>
                    <span>50mm (Large)</span>
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Pages à rogner (optionnel)
                </label>
                <input
                  type="text"
                  value={pages}
                  onChange={(e) => setPages(e.target.value)}
                  placeholder="ex: 1-3, 5, 7-9 ou laissez vide pour toutes"
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="apply-all-pages"
                    defaultChecked
                    className="text-orange-600 rounded"
                  />
                  <label htmlFor="apply-all-pages" className="text-slate-700">
                    Appliquer à toutes les pages
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="preserve-aspect-ratio"
                    defaultChecked
                    className="text-orange-600 rounded"
                  />
                  <label htmlFor="preserve-aspect-ratio" className="text-slate-700">
                    Préserver les proportions
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="center-content"
                    className="text-orange-600 rounded"
                  />
                  <label htmlFor="center-content" className="text-slate-700">
                    Centrer le contenu après rognage
                  </label>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-blue-800 font-medium">Outils de rognage</span>
                </div>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  <li>• Aperçu en temps réel</li>
                  <li>• Sélection précise au pixel près</li>
                  <li>• Grille d'aide pour l'alignement</li>
                  <li>• Annulation/rétablissement des modifications</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <span className="text-blue-800">Rognage du PDF en cours...</span>
            </div>
            {progress > 0 && (
              <div className="mt-3">
                <div className="bg-blue-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-blue-600 mt-1">{progress}% terminé</p>
              </div>
            )}
            {message && (
              <p className="text-sm text-blue-600 mt-2">{message}</p>
            )}
          </div>
        )}

        {/* Error Status */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-red-800">Erreur lors du rognage</h3>
                <p className="text-red-700 text-sm mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success Status */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-medium text-green-800">PDF rogné avec succès!</h3>
                <p className="text-green-700 text-sm mt-1">
                  Votre PDF a été rogné selon vos préférences.
                </p>
                <div className="mt-3">
                  <button
                    onClick={handleDownload}
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Télécharger
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleCrop}
              disabled={isProcessing}
              className="bg-gradient-to-r from-orange-600 to-red-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Rognage en cours...</span>
                </>
              ) : (
                <>
                  <span>Rogner le PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default CropPDF;