import { useState } from 'react';
import { Smartphone, Download, ArrowRight, CheckCircle, AlertCircle, Scan } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const ScanToPDF = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    scanToPDF, resetState
  } = usePDFProcessor();
  
  const [files, setFiles] = useState<File[]>([]);
  const [scanQuality, setScanQuality] = useState<'standard' | 'high' | 'ultra'>('high');
  const [ocrEnabled, setOcrEnabled] = useState(false);
  const [ocrLanguage, setOcrLanguage] = useState<'fr' | 'en' | 'auto'>('auto');
  const [pageSize, setPageSize] = useState<'A4' | 'Letter' | 'auto'>('auto');
  const [combinePages, setCombinePages] = useState(true);
  const [dpi, setDpi] = useState(300);
  const [enhanceImage, setEnhanceImage] = useState(true);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleScan = async () => {
    if (files.length === 0) return;
    
    try {
      await scanToPDF(files, {
        ocr_enabled: ocrEnabled,
        ocr_language: ocrLanguage,
        page_size: pageSize,
        combine_pages: combinePages,
        dpi,
        enhance_image: enhanceImage
      });
    } catch (err) {
      console.error('Scan to PDF failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Numériser au format PDF"
      description="Numérisez des documents avec votre mobile et envoyez-les vers votre navigateur en un clin d'oeil"
      icon={<Smartphone className="w-8 h-8" />}
      color="from-purple-500 to-purple-600"
    >
      <div className="space-y-6">
        <div className="bg-slate-50 p-6 rounded-xl">
          <h3 className="text-lg font-semibold text-slate-700 mb-4">
            Scanner mobile
          </h3>
          
          <div className="space-y-4">
            <div className="text-center">
              <div className="w-32 h-32 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Smartphone className="w-16 h-16 text-purple-600" />
              </div>
              <p className="text-slate-600 mb-4">
                Scannez vos documents directement depuis votre appareil mobile
              </p>
              
              <div className="bg-white p-4 rounded-lg border-2 border-dashed border-purple-300">
                <div className="text-center">
                  <div className="text-2xl mb-2">📱</div>
                  <p className="text-sm text-slate-600">
                    Ouvrez la caméra de votre téléphone pour scanner
                  </p>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Qualité de numérisation
              </label>
              <div className="space-y-2">
                <label className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="quality"
                    value="standard"
                    checked={scanQuality === 'standard'}
                    onChange={(e) => setScanQuality(e.target.value as 'standard')}
                    className="text-purple-600"
                  />
                  <span className="text-slate-700">Standard (plus rapide)</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="quality"
                    value="high"
                    checked={scanQuality === 'high'}
                    onChange={(e) => setScanQuality(e.target.value as 'high')}
                    className="text-purple-600"
                  />
                  <span className="text-slate-700">Haute qualité (recommandé)</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="quality"
                    value="ultra"
                    checked={scanQuality === 'ultra'}
                    onChange={(e) => setScanQuality(e.target.value as 'ultra')}
                    className="text-purple-600"
                  />
                  <span className="text-slate-700">Ultra haute qualité</span>
                </label>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="auto-crop"
                  defaultChecked
                  className="text-purple-600 rounded"
                />
                <label htmlFor="auto-crop" className="text-slate-700">
                  Recadrage automatique
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="auto-enhance"
                  defaultChecked
                  className="text-purple-600 rounded"
                />
                <label htmlFor="auto-enhance" className="text-slate-700">
                  Amélioration automatique
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="multi-page"
                  className="text-purple-600 rounded"
                />
                <label htmlFor="multi-page" className="text-slate-700">
                  Scan multi-pages
                </label>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-blue-800 font-medium">Instructions</span>
              </div>
              <ol className="text-sm text-blue-700 mt-1 space-y-1">
                <li>1. Cliquez sur "Activer le scanner"</li>
                <li>2. Autorisez l'accès à la caméra</li>
                <li>3. Placez le document dans le cadre</li>
                <li>4. Appuyez sur capturer</li>
                <li>5. Téléchargez votre PDF</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-purple-50 border border-purple-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600"></div>
              <span className="text-purple-800 font-medium">Scan en cours...</span>
            </div>
            <div className="w-full bg-purple-200 rounded-full h-2 mb-2">
              <div
                className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-purple-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors du scan</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Scan réussi!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) PDF généré(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {/* Scan Button */}
        {!outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleScan}
              disabled={isProcessing}
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Scan en cours...</span>
                </>
              ) : (
                <>
                  <Scan className="w-5 h-5" />
                  <span>Démarrer le scan</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default ScanToPDF;