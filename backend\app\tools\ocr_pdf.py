"""
PDF OCR tool for extracting text from scanned PDFs and images.
"""
import os
import tempfile
from typing import List, Dict, Any, Optional
import fitz  # PyMuPDF
from PIL import Image
import pytesseract
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.units import inch
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFOCRTool(BasePDFTool):
    """Tool for performing OCR on PDF files and images."""
    
    def __init__(self):
        super().__init__("ocr")
        
        # Supported languages for OCR
        self.supported_languages = {
            "eng": "English",
            "fra": "French", 
            "deu": "German",
            "spa": "Spanish",
            "ita": "Italian",
            "por": "Portuguese",
            "rus": "Russian",
            "chi_sim": "Chinese (Simplified)",
            "chi_tra": "Chinese (Traditional)",
            "jpn": "Japanese",
            "kor": "Korean",
            "ara": "Arabic"
        }
        
        # Output formats
        self.output_formats = {
            "pdf": "Searchable PDF with text layer",
            "text": "Plain text file",
            "docx": "Word document with text",
            "json": "JSON with text and coordinates"
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Perform OCR on PDF files or images.
        
        Args:
            input_files: List of PDF or image file paths for OCR
            output_path: Output directory for OCR results
            parameters: OCR parameters (language, output format, etc.)
            
        Returns:
            List containing paths to OCR output files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 file is required for OCR")
        
        self.validate_input_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        language = params.get("language", "eng")
        output_format = params.get("output_format", "pdf")
        dpi = params.get("dpi", 300)
        preprocessing = params.get("preprocessing", True)
        confidence_threshold = params.get("confidence_threshold", 30)
        
        # Validate parameters
        if language not in self.supported_languages:
            raise ValidationError(f"Unsupported language: {language}. Available: {list(self.supported_languages.keys())}")
        
        if output_format not in self.output_formats:
            raise ValidationError(f"Unsupported output format: {output_format}. Available: {list(self.output_formats.keys())}")
        
        if not isinstance(dpi, int) or dpi < 150 or dpi > 600:
            raise ValidationError("DPI must be between 150 and 600")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting OCR processing",
                input_count=len(input_files),
                language=language,
                output_format=output_format,
                dpi=dpi,
                preprocessing=preprocessing
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Processing file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    
                    if output_format == "pdf":
                        output_filename = f"{base_name}_ocr.pdf"
                    elif output_format == "text":
                        output_filename = f"{base_name}_ocr.txt"
                    elif output_format == "docx":
                        output_filename = f"{base_name}_ocr.docx"
                    elif output_format == "json":
                        output_filename = f"{base_name}_ocr.json"
                    
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Perform OCR
                    await self._perform_ocr(
                        input_file,
                        output_file,
                        language,
                        output_format,
                        dpi,
                        preprocessing,
                        confidence_threshold
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create OCR output file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} OCR completed successfully",
                        input_file=input_file,
                        output_file=output_file,
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to process file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to perform OCR on {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "OCR processing completed successfully",
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during OCR processing: {str(e)}")
    
    async def _perform_ocr(
        self,
        input_file: str,
        output_file: str,
        language: str,
        output_format: str,
        dpi: int,
        preprocessing: bool,
        confidence_threshold: int
    ):
        """Perform OCR on a single file."""
        try:
            # Check if input is PDF or image
            file_ext = os.path.splitext(input_file)[1].lower()
            
            if file_ext == '.pdf':
                # Process PDF
                await self._ocr_pdf(input_file, output_file, language, output_format, dpi, preprocessing, confidence_threshold)
            elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                # Process image
                await self._ocr_image(input_file, output_file, language, output_format, preprocessing, confidence_threshold)
            else:
                raise ValidationError(f"Unsupported file type: {file_ext}")
                
        except Exception as e:
            raise ProcessingError(f"Failed to perform OCR: {str(e)}")
    
    async def _ocr_pdf(
        self,
        input_file: str,
        output_file: str,
        language: str,
        output_format: str,
        dpi: int,
        preprocessing: bool,
        confidence_threshold: int
    ):
        """Perform OCR on a PDF file."""
        try:
            # Open PDF
            doc = fitz.open(input_file)
            
            if doc.needs_pass:
                doc.close()
                raise ProcessingError("Cannot perform OCR on password-protected PDF")
            
            all_text = []
            page_data = []
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Convert page to image
                mat = fitz.Matrix(dpi / 72, dpi / 72)
                pix = page.get_pixmap(matrix=mat)
                
                # Convert to PIL Image
                img_data = pix.tobytes("png")
                with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as temp_img:
                    temp_img.write(img_data)
                    temp_img_path = temp_img.name
                
                try:
                    # Perform OCR on the image
                    text, confidence, word_data = await self._ocr_image_data(
                        temp_img_path, language, preprocessing, confidence_threshold
                    )
                    
                    all_text.append(text)
                    page_data.append({
                        "page": page_num + 1,
                        "text": text,
                        "confidence": confidence,
                        "words": word_data
                    })
                    
                    self.logger.debug(f"OCR completed for page {page_num + 1}", confidence=confidence)
                
                finally:
                    # Clean up temporary image
                    if os.path.exists(temp_img_path):
                        os.remove(temp_img_path)
                
                pix = None
            
            doc.close()
            
            # Save output based on format
            await self._save_ocr_output(output_file, output_format, all_text, page_data)
            
        except Exception as e:
            raise ProcessingError(f"Failed to perform OCR on PDF: {str(e)}")
    
    async def _ocr_image(
        self,
        input_file: str,
        output_file: str,
        language: str,
        output_format: str,
        preprocessing: bool,
        confidence_threshold: int
    ):
        """Perform OCR on an image file."""
        try:
            text, confidence, word_data = await self._ocr_image_data(
                input_file, language, preprocessing, confidence_threshold
            )
            
            page_data = [{
                "page": 1,
                "text": text,
                "confidence": confidence,
                "words": word_data
            }]
            
            await self._save_ocr_output(output_file, output_format, [text], page_data)
            
        except Exception as e:
            raise ProcessingError(f"Failed to perform OCR on image: {str(e)}")
    
    async def _ocr_image_data(
        self,
        image_path: str,
        language: str,
        preprocessing: bool,
        confidence_threshold: int
    ) -> tuple:
        """Perform OCR on an image and return text, confidence, and word data."""
        try:
            # Open image
            image = Image.open(image_path)
            
            # Preprocessing if enabled
            if preprocessing:
                image = self._preprocess_image(image)
            
            # Configure Tesseract
            config = f'--oem 3 --psm 6 -l {language}'
            
            # Extract text
            text = pytesseract.image_to_string(image, config=config)
            
            # Get detailed data with confidence scores
            data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)
            
            # Calculate average confidence
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            # Extract word-level data
            word_data = []
            for i in range(len(data['text'])):
                if int(data['conf'][i]) > confidence_threshold:
                    word_data.append({
                        'text': data['text'][i],
                        'confidence': int(data['conf'][i]),
                        'x': int(data['left'][i]),
                        'y': int(data['top'][i]),
                        'width': int(data['width'][i]),
                        'height': int(data['height'][i])
                    })
            
            return text.strip(), round(avg_confidence, 2), word_data
            
        except Exception as e:
            raise ProcessingError(f"Failed to extract text from image: {str(e)}")
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image to improve OCR accuracy."""
        try:
            # Convert to grayscale
            if image.mode != 'L':
                image = image.convert('L')
            
            # Enhance contrast and sharpness
            from PIL import ImageEnhance, ImageFilter
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)
            
            # Sharpen
            image = image.filter(ImageFilter.SHARPEN)
            
            return image

        except Exception as e:
            self.logger.warning("Failed to preprocess image", error=str(e))
            return image

    async def _save_ocr_output(
        self,
        output_file: str,
        output_format: str,
        all_text: List[str],
        page_data: List[Dict[str, Any]]
    ):
        """Save OCR output in the specified format."""
        try:
            if output_format == "text":
                # Save as plain text
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write('\n\n'.join(all_text))

            elif output_format == "json":
                # Save as JSON
                import json
                output_data = {
                    "total_pages": len(page_data),
                    "full_text": '\n\n'.join(all_text),
                    "pages": page_data
                }
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, indent=2, ensure_ascii=False)

            elif output_format == "docx":
                # Save as Word document
                from docx import Document
                doc = Document()

                for i, text in enumerate(all_text):
                    if i > 0:
                        doc.add_page_break()
                    doc.add_heading(f'Page {i + 1}', level=1)
                    doc.add_paragraph(text)

                doc.save(output_file)

            elif output_format == "pdf":
                # Create searchable PDF
                await self._create_searchable_pdf(output_file, page_data)

        except Exception as e:
            raise ProcessingError(f"Failed to save OCR output: {str(e)}")

    async def _create_searchable_pdf(self, output_file: str, page_data: List[Dict[str, Any]]):
        """Create a searchable PDF with text layer."""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter

            c = canvas.Canvas(output_file, pagesize=letter)

            for page_info in page_data:
                text = page_info['text']

                # Add text to PDF (invisible layer for searchability)
                c.setFont("Helvetica", 12)

                # Split text into lines and add to PDF
                lines = text.split('\n')
                y_position = 750

                for line in lines:
                    if line.strip():
                        c.drawString(50, y_position, line.strip())
                        y_position -= 15

                        # Start new page if needed
                        if y_position < 50:
                            c.showPage()
                            y_position = 750

                if page_info != page_data[-1]:  # Not the last page
                    c.showPage()

            c.save()

        except Exception as e:
            raise ProcessingError(f"Failed to create searchable PDF: {str(e)}")

    def get_ocr_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available OCR options."""
        return {
            "language": {
                "description": "OCR language",
                "type": "string",
                "options": list(self.supported_languages.keys()),
                "default": "eng",
                "language_names": self.supported_languages
            },
            "output_format": {
                "description": "Output format",
                "type": "string",
                "options": list(self.output_formats.keys()),
                "default": "pdf",
                "format_descriptions": self.output_formats
            },
            "dpi": {
                "description": "Image resolution for PDF conversion",
                "type": "integer",
                "min": 150,
                "max": 600,
                "default": 300
            },
            "preprocessing": {
                "description": "Apply image preprocessing to improve OCR accuracy",
                "type": "boolean",
                "default": True
            },
            "confidence_threshold": {
                "description": "Minimum confidence threshold for word recognition",
                "type": "integer",
                "min": 0,
                "max": 100,
                "default": 30
            }
        }

    def get_supported_languages(self) -> Dict[str, str]:
        """Get supported OCR languages."""
        return self.supported_languages.copy()

    def get_output_formats(self) -> Dict[str, str]:
        """Get supported output formats."""
        return self.output_formats.copy()

    async def test_ocr_setup(self) -> Dict[str, Any]:
        """Test if OCR dependencies are properly installed."""
        try:
            # Test Tesseract installation
            version = pytesseract.get_tesseract_version()

            # Test available languages
            available_langs = pytesseract.get_languages()

            supported_langs = []
            for lang_code, lang_name in self.supported_languages.items():
                if lang_code in available_langs:
                    supported_langs.append({"code": lang_code, "name": lang_name})

            return {
                "tesseract_installed": True,
                "tesseract_version": str(version),
                "available_languages": len(available_langs),
                "supported_languages": supported_langs,
                "status": "ready"
            }

        except Exception as e:
            return {
                "tesseract_installed": False,
                "error": str(e),
                "status": "error",
                "message": "Tesseract OCR is not properly installed or configured"
            }


# Create tool instance
ocr_pdf_tool = PDFOCRTool()
