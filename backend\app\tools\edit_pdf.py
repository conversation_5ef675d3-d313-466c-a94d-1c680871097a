"""
PDF editing tool for modifying text and content within PDF files.
"""
import os
from typing import List, Dict, Any, Optional
import structlog

from .base_tool import Base<PERSON><PERSON><PERSON>, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFEditTool(BasePDFTool):
    """Tool for editing text and content within PDF files."""
    
    def __init__(self):
        super().__init__("edit")
        
        # Edit operations
        self.edit_operations = {
            "replace_text": "Replace specific text with new text",
            "add_text": "Add new text at specified position",
            "remove_text": "Remove specific text",
            "highlight_text": "Highlight specific text",
            "add_annotation": "Add annotation/comment",
            "add_shape": "Add shapes (rectangle, circle, line)",
            "add_stamp": "Add stamp or seal"
        }
        
        # Text alignment options
        self.text_alignments = {
            "left": "Left aligned",
            "center": "Center aligned",
            "right": "Right aligned"
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Edit PDF files with specified operations.
        
        Args:
            input_files: List of PDF file paths to edit
            output_path: Output directory for edited PDFs
            parameters: Edit parameters (operations, text, positions, etc.)
            
        Returns:
            List containing paths to edited PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for editing")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        operations = params.get("operations", [])
        pages = params.get("pages", "all")
        
        # Validate operations
        if not operations:
            raise ValidationError("At least one edit operation is required")
        
        for operation in operations:
            if "type" not in operation:
                raise ValidationError("Each operation must have a 'type' field")
            
            if operation["type"] not in self.edit_operations:
                raise ValidationError(f"Invalid operation type: {operation['type']}. Available: {list(self.edit_operations.keys())}")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF editing",
                input_count=len(input_files),
                operations_count=len(operations),
                pages=pages
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Editing file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}_edited.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Edit the PDF
                    edit_result = await self._edit_pdf(
                        input_file,
                        output_file,
                        operations,
                        pages
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create edited PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} edited successfully",
                        input_file=input_file,
                        output_file=output_file,
                        operations_applied=edit_result["operations_applied"],
                        pages_modified=edit_result["pages_modified"],
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to edit file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to edit {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PDF editing completed successfully",
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF editing: {str(e)}")
    
    async def _edit_pdf(
        self,
        input_file: str,
        output_file: str,
        operations: List[Dict[str, Any]],
        pages: str
    ) -> Dict[str, Any]:
        """Edit a PDF file with specified operations."""
        try:
            import fitz  # PyMuPDF
            
            # Open PDF
            doc = fitz.open(input_file)
            
            if doc.needs_pass:
                doc.close()
                raise ProcessingError("Cannot edit password-protected PDF")
            
            total_pages = len(doc)
            
            # Determine which pages to edit
            pages_to_edit = self._parse_pages_parameter(pages, total_pages)
            
            operations_applied = 0
            pages_modified = set()
            
            # Apply each operation
            for operation in operations:
                operation_type = operation["type"]
                
                # Apply operation to specified pages
                for page_num in range(total_pages):
                    if (page_num + 1) not in pages_to_edit:
                        continue
                    
                    page = doc[page_num]
                    
                    try:
                        if operation_type == "replace_text":
                            if await self._replace_text(page, operation):
                                operations_applied += 1
                                pages_modified.add(page_num + 1)
                        
                        elif operation_type == "add_text":
                            if await self._add_text(page, operation):
                                operations_applied += 1
                                pages_modified.add(page_num + 1)
                        
                        elif operation_type == "remove_text":
                            if await self._remove_text(page, operation):
                                operations_applied += 1
                                pages_modified.add(page_num + 1)
                        
                        elif operation_type == "highlight_text":
                            if await self._highlight_text(page, operation):
                                operations_applied += 1
                                pages_modified.add(page_num + 1)
                        
                        elif operation_type == "add_annotation":
                            if await self._add_annotation(page, operation):
                                operations_applied += 1
                                pages_modified.add(page_num + 1)
                        
                        elif operation_type == "add_shape":
                            if await self._add_shape(page, operation):
                                operations_applied += 1
                                pages_modified.add(page_num + 1)
                        
                        elif operation_type == "add_stamp":
                            if await self._add_stamp(page, operation):
                                operations_applied += 1
                                pages_modified.add(page_num + 1)
                        
                    except Exception as e:
                        self.logger.warning(f"Failed to apply {operation_type} on page {page_num + 1}: {str(e)}")
            
            # Save edited PDF
            doc.save(output_file, garbage=4, deflate=True, clean=True)
            doc.close()
            
            return {
                "operations_applied": operations_applied,
                "pages_modified": len(pages_modified),
                "modified_page_numbers": list(pages_modified)
            }
            
        except Exception as e:
            raise ProcessingError(f"Failed to edit PDF: {str(e)}")
    
    async def _replace_text(self, page, operation: Dict[str, Any]) -> bool:
        """Replace text on a page."""
        try:
            old_text = operation.get("old_text", "")
            new_text = operation.get("new_text", "")
            
            if not old_text:
                return False
            
            # Get text instances
            text_instances = page.search_for(old_text)
            
            if not text_instances:
                return False
            
            # Replace each instance
            for inst in text_instances:
                # Add redaction annotation to cover old text
                page.add_redact_annot(inst, new_text)
            
            # Apply redactions
            page.apply_redactions()
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Text replacement failed: {str(e)}")
            return False
    
    async def _add_text(self, page, operation: Dict[str, Any]) -> bool:
        """Add text to a page."""
        try:
            text = operation.get("text", "")
            x = operation.get("x", 100)
            y = operation.get("y", 100)
            font_size = operation.get("font_size", 12)
            color = operation.get("color", [0, 0, 0])  # RGB
            
            if not text:
                return False
            
            # Insert text
            page.insert_text(
                fitz.Point(x, y),
                text,
                fontsize=font_size,
                color=color
            )
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Text addition failed: {str(e)}")
            return False
    
    async def _remove_text(self, page, operation: Dict[str, Any]) -> bool:
        """Remove text from a page."""
        try:
            text_to_remove = operation.get("text", "")
            
            if not text_to_remove:
                return False
            
            # Find text instances
            text_instances = page.search_for(text_to_remove)
            
            if not text_instances:
                return False
            
            # Add redaction annotations
            for inst in text_instances:
                page.add_redact_annot(inst)
            
            # Apply redactions
            page.apply_redactions()
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Text removal failed: {str(e)}")
            return False
    
    async def _highlight_text(self, page, operation: Dict[str, Any]) -> bool:
        """Highlight text on a page."""
        try:
            text_to_highlight = operation.get("text", "")
            color = operation.get("color", [1, 1, 0])  # Yellow by default
            
            if not text_to_highlight:
                return False
            
            # Find text instances
            text_instances = page.search_for(text_to_highlight)
            
            if not text_instances:
                return False
            
            # Add highlight annotations
            for inst in text_instances:
                highlight = page.add_highlight_annot(inst)
                highlight.set_colors(stroke=color)
                highlight.update()
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Text highlighting failed: {str(e)}")
            return False
    
    async def _add_annotation(self, page, operation: Dict[str, Any]) -> bool:
        """Add annotation to a page."""
        try:
            x = operation.get("x", 100)
            y = operation.get("y", 100)
            content = operation.get("content", "Note")
            
            # Add text annotation
            point = fitz.Point(x, y)
            annot = page.add_text_annot(point, content)
            annot.set_info(content=content)
            annot.update()
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Annotation addition failed: {str(e)}")
            return False
    
    async def _add_shape(self, page, operation: Dict[str, Any]) -> bool:
        """Add shape to a page."""
        try:
            shape_type = operation.get("shape_type", "rectangle")
            x1 = operation.get("x1", 100)
            y1 = operation.get("y1", 100)
            x2 = operation.get("x2", 200)
            y2 = operation.get("y2", 200)
            color = operation.get("color", [0, 0, 1])  # Blue by default
            width = operation.get("width", 1)
            
            rect = fitz.Rect(x1, y1, x2, y2)
            
            if shape_type == "rectangle":
                page.draw_rect(rect, color=color, width=width)
            elif shape_type == "circle":
                page.draw_circle(fitz.Point((x1 + x2) / 2, (y1 + y2) / 2), 
                               min(abs(x2 - x1), abs(y2 - y1)) / 2, 
                               color=color, width=width)
            elif shape_type == "line":
                page.draw_line(fitz.Point(x1, y1), fitz.Point(x2, y2), 
                              color=color, width=width)
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Shape addition failed: {str(e)}")
            return False
    
    async def _add_stamp(self, page, operation: Dict[str, Any]) -> bool:
        """Add stamp to a page."""
        try:
            x = operation.get("x", 100)
            y = operation.get("y", 100)
            stamp_text = operation.get("stamp_text", "APPROVED")
            font_size = operation.get("font_size", 20)
            color = operation.get("color", [1, 0, 0])  # Red by default
            
            # Create stamp rectangle
            text_width = len(stamp_text) * font_size * 0.6
            text_height = font_size * 1.2
            
            stamp_rect = fitz.Rect(x, y, x + text_width, y + text_height)
            
            # Draw stamp border
            page.draw_rect(stamp_rect, color=color, width=2)
            
            # Add stamp text
            page.insert_text(
                fitz.Point(x + 5, y + font_size),
                stamp_text,
                fontsize=font_size,
                color=color
            )
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Stamp addition failed: {str(e)}")
            return False
    
    def _parse_pages_parameter(self, pages: str, total_pages: int) -> set:
        """Parse the pages parameter to get set of page numbers to edit."""
        if pages == "all":
            return set(range(1, total_pages + 1))
        else:
            # Parse specific pages (e.g., "1,3,5-7,10")
            page_numbers = set()
            
            for part in pages.split(','):
                part = part.strip()
                
                if '-' in part:
                    # Range like "5-7"
                    start_str, end_str = part.split('-', 1)
                    start = int(start_str.strip())
                    end = int(end_str.strip())
                    
                    if start < 1 or end > total_pages or start > end:
                        raise ValidationError(f"Invalid page range: {part}")
                    
                    page_numbers.update(range(start, end + 1))
                else:
                    # Single page like "1"
                    page = int(part)
                    
                    if page < 1 or page > total_pages:
                        raise ValidationError(f"Invalid page number: {page}")
                    
                    page_numbers.add(page)
            
            return page_numbers
    
    def get_edit_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available edit options."""
        return {
            "operations": {
                "description": "List of edit operations to perform",
                "type": "array",
                "operation_types": self.edit_operations,
                "example": [
                    {
                        "type": "replace_text",
                        "old_text": "Draft",
                        "new_text": "Final"
                    },
                    {
                        "type": "add_text",
                        "text": "Confidential",
                        "x": 100,
                        "y": 50,
                        "font_size": 14,
                        "color": [1, 0, 0]
                    }
                ]
            },
            "pages": {
                "description": "Pages to edit",
                "type": "string",
                "options": ["all", "specific (e.g., 1,3,5-7)"],
                "default": "all"
            }
        }
    
    def get_edit_operations(self) -> Dict[str, str]:
        """Get available edit operations."""
        return self.edit_operations.copy()
    
    def get_text_alignments(self) -> Dict[str, str]:
        """Get available text alignments."""
        return self.text_alignments.copy()


# Create tool instance
edit_pdf_tool = PDFEditTool()
