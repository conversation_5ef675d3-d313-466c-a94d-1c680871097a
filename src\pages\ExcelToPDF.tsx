import React, { useState } from 'react';
import { FileText, Download, ArrowRight, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const ExcelToPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [orientation, setOrientation] = useState<'auto' | 'portrait' | 'landscape'>('auto');
  const [sheets, setSheets] = useState('');
  const [fitToPage, setFitToPage] = useState(true);
  const [includeGridlines, setIncludeGridlines] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { downloadAllFiles } = useSession();

  const {
    isProcessing,
    progress,
    message,
    error,
    outputFiles,
    taskId,
    convertExcelToPDF,
    resetState
  } = usePDFProcessor();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleConvert = async () => {
    if (files.length === 0) return;

    try {
      const parameters = {
        orientation: orientation,
        sheets: sheets || undefined,
        fit_to_page: fitToPage,
        include_gridlines: includeGridlines,
      };

      await convertExcelToPDF(files, parameters, {
        onProgress: (progress, message) => {
          console.log(`Progress: ${progress}% - ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Conversion completed:', outputFiles);
        },
        onError: (error) => {
          console.error('Conversion failed:', error);
        }
      });
    } catch (err) {
      console.error('Conversion operation failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Excel en PDF"
      description="Facilitez la lecture de vos feuilles de calcul EXCEL en les convertissant en PDF"
      icon={<FileText className="w-8 h-8" />}
      color="from-teal-500 to-teal-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".xls,.xlsx"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre feuille de calcul Excel"
          description="Glissez-déposez un fichier XLS ou XLSX ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de conversion
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="all-sheets"
                  defaultChecked
                  className="text-teal-600 rounded"
                />
                <label htmlFor="all-sheets" className="text-slate-700">
                  Inclure toutes les feuilles de calcul
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="preserve-formatting"
                  defaultChecked
                  className="text-teal-600 rounded"
                />
                <label htmlFor="preserve-formatting" className="text-slate-700">
                  Préserver le formatage et les couleurs
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Orientation:
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="orientation"
                      value="auto"
                      checked={orientation === 'auto'}
                      onChange={(e) => setOrientation(e.target.value as any)}
                      className="text-teal-600"
                    />
                    <span className="text-sm text-slate-700">Automatique</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="orientation"
                      value="portrait"
                      checked={orientation === 'portrait'}
                      onChange={(e) => setOrientation(e.target.value as any)}
                      className="text-teal-600"
                    />
                    <span className="text-sm text-slate-700">Portrait</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="orientation"
                      value="landscape"
                      checked={orientation === 'landscape'}
                      onChange={(e) => setOrientation(e.target.value as any)}
                      className="text-teal-600"
                    />
                    <span className="text-sm text-slate-700">Paysage</span>
                  </label>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="fitToPage"
                  checked={fitToPage}
                  onChange={(e) => setFitToPage(e.target.checked)}
                  className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
                />
                <label htmlFor="fitToPage" className="text-sm text-slate-700">
                  Ajuster automatiquement à la page
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="includeGridlines"
                  checked={includeGridlines}
                  onChange={(e) => setIncludeGridlines(e.target.checked)}
                  className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
                />
                <label htmlFor="includeGridlines" className="text-sm text-slate-700">
                  Inclure les lignes de grille
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-teal-50 border border-teal-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-teal-600"></div>
              <span className="text-teal-800 font-medium">Conversion en cours...</span>
            </div>
            <div className="w-full bg-teal-200 rounded-full h-2 mb-2">
              <div
                className="bg-teal-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-teal-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la conversion</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Conversion réussie!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) PDF généré(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {/* Convert Button */}
        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-teal-600 to-green-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <FileText className="w-5 h-5" />
                  <span>Convertir en PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default ExcelToPDF;