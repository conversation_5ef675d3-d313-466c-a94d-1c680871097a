/**
 * Session context provider for managing user sessions across the application
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { sessionManager, SessionInfo, TaskInfo } from '../services/sessionManager';

interface SessionContextType {
  session: SessionInfo | null;
  isLoading: boolean;
  error: string | null;
  tasks: TaskInfo[];
  activeTasks: TaskInfo[];
  completedTasks: TaskInfo[];
  failedTasks: TaskInfo[];
  createSession: () => Promise<SessionInfo>;
  ensureSession: () => Promise<SessionInfo>;
  clearSession: () => void;
  addTask: (taskId: string, toolName: string) => TaskInfo;
  updateTask: (taskId: string, updates: Partial<TaskInfo>) => TaskInfo | null;
  getTask: (taskId: string) => TaskInfo | null;
  removeTask: (taskId: string) => boolean;
  clearCompletedTasks: () => void;
  pollTaskStatus: (taskId: string, onUpdate?: (task: TaskInfo) => void) => Promise<TaskInfo>;
  downloadFile: (taskId: string, filename: string) => Promise<void>;
  downloadAllFiles: (taskId: string) => Promise<void>;
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

export const SessionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<SessionInfo | null>(sessionManager.getCurrentSession());
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [tasks, setTasks] = useState<TaskInfo[]>(sessionManager.getAllTasks());

  // Update tasks when they change
  useEffect(() => {
    const updateTasks = () => {
      setTasks(sessionManager.getAllTasks());
    };

    // Set up interval to check for task updates
    const intervalId = setInterval(updateTasks, 2000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, []);

  // Create a new session
  const createSession = async (): Promise<SessionInfo> => {
    setIsLoading(true);
    setError(null);

    try {
      const newSession = await sessionManager.createSession();
      setSession(newSession);
      return newSession;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create session';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Ensure a valid session exists
  const ensureSession = async (): Promise<SessionInfo> => {
    if (session && !sessionManager.isSessionExpired()) {
      return session;
    }

    return createSession();
  };

  // Clear the current session
  const clearSession = (): void => {
    sessionManager.clearSession();
    setSession(null);
    setTasks([]);
  };

  // Add a new task
  const addTask = (taskId: string, toolName: string): TaskInfo => {
    const newTask = sessionManager.addTask(taskId, toolName);
    setTasks(sessionManager.getAllTasks());
    return newTask;
  };

  // Update an existing task
  const updateTask = (taskId: string, updates: Partial<TaskInfo>): TaskInfo | null => {
    const updatedTask = sessionManager.updateTask(taskId, updates);
    setTasks(sessionManager.getAllTasks());
    return updatedTask;
  };

  // Get a specific task
  const getTask = (taskId: string): TaskInfo | null => {
    return sessionManager.getTask(taskId);
  };

  // Remove a task
  const removeTask = (taskId: string): boolean => {
    const removed = sessionManager.removeTask(taskId);
    if (removed) {
      setTasks(sessionManager.getAllTasks());
    }
    return removed;
  };

  // Clear completed tasks
  const clearCompletedTasks = (): void => {
    sessionManager.clearCompletedTasks();
    setTasks(sessionManager.getAllTasks());
  };

  // Poll task status
  const pollTaskStatus = async (
    taskId: string,
    onUpdate?: (task: TaskInfo) => void
  ): Promise<TaskInfo> => {
    try {
      const updatedTask = await sessionManager.pollTaskStatus(taskId, (task) => {
        setTasks(sessionManager.getAllTasks());
        if (onUpdate) onUpdate(task);
      });
      
      return updatedTask;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to poll task status';
      setError(errorMessage);
      throw err;
    }
  };

  // Download a file
  const downloadFile = async (taskId: string, filename: string): Promise<void> => {
    try {
      await sessionManager.downloadFile(taskId, filename);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to download file';
      setError(errorMessage);
      throw err;
    }
  };

  // Download all files for a task
  const downloadAllFiles = async (taskId: string): Promise<void> => {
    try {
      await sessionManager.downloadAllFiles(taskId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to download files';
      setError(errorMessage);
      throw err;
    }
  };

  // Filter tasks by status
  const activeTasks = tasks.filter(task => task.status === 'processing');
  const completedTasks = tasks.filter(task => task.status === 'completed');
  const failedTasks = tasks.filter(task => task.status === 'failed');

  const value = {
    session,
    isLoading,
    error,
    tasks,
    activeTasks,
    completedTasks,
    failedTasks,
    createSession,
    ensureSession,
    clearSession,
    addTask,
    updateTask,
    getTask,
    removeTask,
    clearCompletedTasks,
    pollTaskStatus,
    downloadFile,
    downloadAllFiles,
  };

  return <SessionContext.Provider value={value}>{children}</SessionContext.Provider>;
};

// Custom hook to use the session context
export const useSession = (): SessionContextType => {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider');
  }
  return context;
};

export default SessionContext;
