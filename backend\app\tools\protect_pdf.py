"""
PDF protection/encryption tool for adding password protection to PDFs.
"""
import os
from typing import List, Dict, Any, Optional
from PyPDF2 import <PERSON>dfWriter, PdfReader
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFProtectTool(BasePDFTool):
    """Tool for adding password protection and encryption to PDF files."""
    
    def __init__(self):
        super().__init__("protect")
        
        # Permission flags for PDF security
        self.permission_flags = {
            "print": 4,           # Allow printing
            "modify": 8,          # Allow document modification
            "copy": 16,           # Allow text/graphics copying
            "annotations": 32,    # Allow adding annotations
            "forms": 256,         # Allow filling forms
            "accessibility": 512, # Allow accessibility (screen readers)
            "assemble": 1024,     # Allow document assembly
            "print_high": 2048    # Allow high-quality printing
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Add password protection to PDF files.
        
        Args:
            input_files: List of PDF file paths to protect
            output_path: Output directory for protected PDFs
            parameters: Protection parameters (passwords, permissions, etc.)
            
        Returns:
            List containing paths to protected PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for protection")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        user_password = params.get("user_password", "")  # Password to open the document
        owner_password = params.get("owner_password", "")  # Password to modify permissions
        permissions = params.get("permissions", {})  # Dictionary of permission settings
        
        # Validate parameters
        if not user_password and not owner_password:
            raise ValidationError("At least one password (user or owner) must be provided")
        
        # If only user password is provided, use it as owner password too
        if user_password and not owner_password:
            owner_password = user_password
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF protection",
                input_count=len(input_files),
                has_user_password=bool(user_password),
                has_owner_password=bool(owner_password),
                permissions=permissions
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Protecting file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}_protected.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Protect the PDF
                    await self._protect_pdf(
                        input_file, 
                        output_file, 
                        user_password,
                        owner_password,
                        permissions
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create protected PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} protected successfully",
                        input_file=input_file,
                        output_file=output_file,
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to protect file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to protect {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PDF protection completed successfully",
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF protection: {str(e)}")
    
    async def _protect_pdf(
        self, 
        input_file: str, 
        output_file: str, 
        user_password: str,
        owner_password: str,
        permissions: Dict[str, bool]
    ):
        """Protect a single PDF file with password and permissions."""
        try:
            # Read the input PDF
            with open(input_file, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                # Check if PDF is already encrypted
                if pdf_reader.is_encrypted:
                    self.logger.warning("PDF is already encrypted", file_path=input_file)
                    # Could try to decrypt with empty password, but for now we'll error
                    raise ProcessingError("Cannot protect an already encrypted PDF")
                
                # Create PDF writer
                pdf_writer = PdfWriter()
                
                # Copy all pages
                for page_num in range(len(pdf_reader.pages)):
                    pdf_writer.add_page(pdf_reader.pages[page_num])
                
                # Copy metadata if available
                if pdf_reader.metadata:
                    pdf_writer.add_metadata(pdf_reader.metadata)
                
                # Calculate permission flags
                permission_flags = self._calculate_permission_flags(permissions)
                
                # Encrypt the PDF
                pdf_writer.encrypt(
                    user_password=user_password,
                    owner_password=owner_password,
                    use_128bit=True,
                    permissions_flag=permission_flags
                )
                
                # Write the protected PDF
                with open(output_file, 'wb') as output_pdf:
                    pdf_writer.write(output_pdf)
                
                self.logger.debug(
                    "PDF encryption applied",
                    input_file=input_file,
                    output_file=output_file,
                    permission_flags=permission_flags
                )
                
        except Exception as e:
            raise ProcessingError(f"Failed to protect PDF: {str(e)}")
    
    def _calculate_permission_flags(self, permissions: Dict[str, bool]) -> int:
        """Calculate permission flags based on permission settings."""
        # Start with no permissions (all restricted)
        flags = 0
        
        # Add permissions based on settings
        for permission, allowed in permissions.items():
            if allowed and permission in self.permission_flags:
                flags |= self.permission_flags[permission]
        
        # If no specific permissions are set, allow basic operations
        if not permissions:
            # Default: allow printing, copying, and accessibility
            flags = (
                self.permission_flags["print"] |
                self.permission_flags["copy"] |
                self.permission_flags["accessibility"]
            )
        
        return flags
    
    def get_permission_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available permission options."""
        return {
            "print": {
                "description": "Allow printing the document",
                "type": "boolean",
                "default": True
            },
            "modify": {
                "description": "Allow modifying the document",
                "type": "boolean",
                "default": False
            },
            "copy": {
                "description": "Allow copying text and graphics",
                "type": "boolean",
                "default": True
            },
            "annotations": {
                "description": "Allow adding annotations and comments",
                "type": "boolean",
                "default": False
            },
            "forms": {
                "description": "Allow filling form fields",
                "type": "boolean",
                "default": True
            },
            "accessibility": {
                "description": "Allow accessibility features (screen readers)",
                "type": "boolean",
                "default": True
            },
            "assemble": {
                "description": "Allow document assembly (page insertion, rotation)",
                "type": "boolean",
                "default": False
            },
            "print_high": {
                "description": "Allow high-quality printing",
                "type": "boolean",
                "default": True
            }
        }
    
    def get_protection_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available protection options."""
        return {
            "user_password": {
                "description": "Password required to open the document",
                "type": "string",
                "required": False,
                "note": "If not provided, document can be opened without password"
            },
            "owner_password": {
                "description": "Password required to modify permissions",
                "type": "string", 
                "required": False,
                "note": "If not provided, user_password will be used"
            },
            "permissions": {
                "description": "Document permissions",
                "type": "object",
                "properties": self.get_permission_options()
            }
        }
    
    async def check_pdf_security(self, file_path: str) -> Dict[str, Any]:
        """Check the current security status of a PDF file."""
        try:
            with open(file_path, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                info = {
                    "filename": os.path.basename(file_path),
                    "is_encrypted": pdf_reader.is_encrypted,
                    "page_count": len(pdf_reader.pages),
                    "size_mb": round(self.get_file_size_mb(file_path), 2)
                }
                
                if pdf_reader.is_encrypted:
                    info["security_note"] = "Document is already password protected"
                else:
                    info["security_note"] = "Document is not password protected"
                
                # Try to get metadata
                try:
                    if pdf_reader.metadata:
                        info["title"] = pdf_reader.metadata.get("/Title", "")
                        info["author"] = pdf_reader.metadata.get("/Author", "")
                        info["creator"] = pdf_reader.metadata.get("/Creator", "")
                except Exception:
                    pass
                
                return info
                
        except Exception as e:
            self.logger.error("Failed to check PDF security", file_path=file_path, error=str(e))
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }
    
    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """Validate password strength and provide recommendations."""
        if not password:
            return {
                "valid": False,
                "strength": "none",
                "message": "Password is required"
            }
        
        length = len(password)
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        score = 0
        if length >= 8:
            score += 1
        if length >= 12:
            score += 1
        if has_upper:
            score += 1
        if has_lower:
            score += 1
        if has_digit:
            score += 1
        if has_special:
            score += 1
        
        if score >= 5:
            strength = "strong"
        elif score >= 3:
            strength = "medium"
        else:
            strength = "weak"
        
        recommendations = []
        if length < 8:
            recommendations.append("Use at least 8 characters")
        if not has_upper:
            recommendations.append("Include uppercase letters")
        if not has_lower:
            recommendations.append("Include lowercase letters")
        if not has_digit:
            recommendations.append("Include numbers")
        if not has_special:
            recommendations.append("Include special characters")
        
        return {
            "valid": score >= 2,
            "strength": strength,
            "score": score,
            "recommendations": recommendations,
            "message": f"Password strength: {strength}"
        }


# Create tool instance
protect_pdf_tool = PDFProtectTool()
