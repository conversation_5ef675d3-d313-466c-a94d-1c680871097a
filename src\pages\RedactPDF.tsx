import { useState } from 'react';
import { Scissors, Download, ArrowRight, CheckCircle, AlertCircle, EyeOff } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const RedactPDF = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    redactPDF, resetState
  } = usePDFProcessor();
  
  const [files, setFiles] = useState<File[]>([]);
  const [redactionType, setRedactionType] = useState<'manual' | 'automatic' | 'pattern'>('automatic');
  const [patterns, setPatterns] = useState<string[]>(['']);
  const [method, setMethod] = useState<'blackout' | 'remove'>('blackout');
  const [pages, setPages] = useState<string>('');
  const [caseSensitive, setCaseSensitive] = useState(false);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handlePatternChange = (index: number, value: string) => {
    const newPatterns = [...patterns];
    newPatterns[index] = value;
    setPatterns(newPatterns);
  };

  const addPattern = () => {
    setPatterns([...patterns, '']);
  };

  const removePattern = (index: number) => {
    setPatterns(patterns.filter((_, i) => i !== index));
  };

  const handleRedact = async () => {
    if (files.length === 0) return;
    
    try {
      await redactPDF(files, {
        patterns: patterns.filter(p => p.trim()),
        method,
        pages: pages || undefined,
        case_sensitive: caseSensitive
      });
    } catch (err) {
      console.error('PDF redaction failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Censurer PDF"
      description="Censurez le texte et les graphiques pour supprimer définitivement les informations sensibles d'un PDF"
      icon={<Scissors className="w-8 h-8" />}
      color="from-red-500 to-red-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de censure
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Type de censure
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="redactionType"
                      value="manual"
                      checked={redactionType === 'manual'}
                      onChange={(e) => setRedactionType(e.target.value as 'manual')}
                      className="text-red-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Censure manuelle</span>
                      <p className="text-sm text-slate-500">Sélectionnez manuellement les zones à censurer</p>
                    </div>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="redactionType"
                      value="automatic"
                      checked={redactionType === 'automatic'}
                      onChange={(e) => setRedactionType(e.target.value as 'automatic')}
                      className="text-red-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Censure automatique</span>
                      <p className="text-sm text-slate-500">Détection automatique d'informations sensibles</p>
                    </div>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="redactionType"
                      value="pattern"
                      checked={redactionType === 'pattern'}
                      onChange={(e) => setRedactionType(e.target.value as 'pattern')}
                      className="text-red-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Censure par motif</span>
                      <p className="text-sm text-slate-500">Recherche et censure par mots-clés ou expressions</p>
                    </div>
                  </label>
                </div>
              </div>

              {redactionType === 'automatic' && (
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="redact-emails"
                      defaultChecked
                      className="text-red-600 rounded"
                    />
                    <label htmlFor="redact-emails" className="text-slate-700">
                      Adresses e-mail
                    </label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="redact-phones"
                      defaultChecked
                      className="text-red-600 rounded"
                    />
                    <label htmlFor="redact-phones" className="text-slate-700">
                      Numéros de téléphone
                    </label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="redact-ssn"
                      defaultChecked
                      className="text-red-600 rounded"
                    />
                    <label htmlFor="redact-ssn" className="text-slate-700">
                      Numéros de sécurité sociale
                    </label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="redact-credit-cards"
                      defaultChecked
                      className="text-red-600 rounded"
                    />
                    <label htmlFor="redact-credit-cards" className="text-slate-700">
                      Numéros de carte de crédit
                    </label>
                  </div>
                </div>
              )}

              {redactionType === 'pattern' && (
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Mots-clés à censurer (séparés par des virgules)
                  </label>
                  <textarea
                    placeholder="Ex: confidentiel, secret, privé, nom de société"
                    className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 h-20"
                  />
                </div>
              )}

              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm text-yellow-800 font-medium">Attention</span>
                </div>
                <p className="text-sm text-yellow-700 mt-1">
                  La censure est définitive et irréversible. Assurez-vous de conserver une copie de sauvegarde de votre document original.
                </p>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-blue-800 font-medium">Outils de censure</span>
                </div>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  <li>• Rectangle noir pour masquer le texte</li>
                  <li>• Suppression définitive du contenu</li>
                  <li>• Préservation de la mise en page</li>
                  <li>• Aperçu avant application</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-red-600"></div>
              <span className="text-red-800 font-medium">Censure en cours...</span>
            </div>
            <div className="w-full bg-red-200 rounded-full h-2 mb-2">
              <div
                className="bg-red-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-red-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la censure</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Censure réussie!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) censuré(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {/* Redact Button */}
        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleRedact}
              disabled={isProcessing}
              className="bg-gradient-to-r from-red-600 to-rose-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Censure en cours...</span>
                </>
              ) : (
                <>
                  <EyeOff className="w-5 h-5" />
                  <span>Censurer le PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default RedactPDF;