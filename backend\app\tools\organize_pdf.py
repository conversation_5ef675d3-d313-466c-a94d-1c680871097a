"""
PDF organize tool for reordering, removing, and organizing pages.
"""
import os
from typing import List, Dict, Any, Optional, Set, Tuple
from PyPDF2 import PdfWriter, PdfReader
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFOrganizeTool(BasePDFTool):
    """Tool for organizing PDF pages - reorder, remove, duplicate, etc."""
    
    def __init__(self):
        super().__init__("organize")
        
        # Organization operations
        self.operations = {
            "reorder": "Reorder pages according to specified sequence",
            "remove": "Remove specified pages",
            "duplicate": "Duplicate specified pages",
            "reverse": "Reverse the order of all pages",
            "extract_odd": "Extract only odd-numbered pages",
            "extract_even": "Extract only even-numbered pages",
            "interleave": "Interleave pages from two sections",
            "custom": "Custom organization with multiple operations"
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Organize PDF pages according to specified operations.
        
        Args:
            input_files: List of PDF file paths to organize (typically one file)
            output_path: Output directory for organized PDFs
            parameters: Organization parameters (operation, page specifications, etc.)
            
        Returns:
            List containing paths to organized PDF files
        """
        # Validate inputs
        if len(input_files) != 1:
            raise ValidationError("PDF organize requires exactly 1 input file")
        
        input_file = input_files[0]
        self.validate_input_files([input_file])
        self.validate_pdf_files([input_file])
        
        # Prepare parameters
        params = parameters or {}
        operation = params.get("operation", "reorder")
        page_spec = params.get("page_spec", "")
        preserve_bookmarks = params.get("preserve_bookmarks", False)
        
        # Validate parameters
        if operation not in self.operations:
            raise ValidationError(f"Invalid operation: {operation}. Available: {list(self.operations.keys())}")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        try:
            self.logger.info(
                "Starting PDF organization",
                input_file=input_file,
                operation=operation,
                page_spec=page_spec,
                preserve_bookmarks=preserve_bookmarks
            )
            
            # Generate output filename
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            output_filename = f"{base_name}_organized.pdf"
            output_file = os.path.join(output_path, output_filename)
            
            # Organize the PDF
            organization_result = await self._organize_pdf(
                input_file,
                output_file,
                operation,
                page_spec,
                preserve_bookmarks
            )
            
            # Verify output file was created
            if not os.path.exists(output_file):
                raise ProcessingError("Failed to create organized PDF file")
            
            output_size = self.get_file_size_mb(output_file)
            input_size = self.get_file_size_mb(input_file)
            
            self.logger.info(
                "PDF organization completed successfully",
                input_file=input_file,
                output_file=output_file,
                operation=operation,
                original_pages=organization_result["original_pages"],
                final_pages=organization_result["final_pages"],
                input_size_mb=round(input_size, 2),
                output_size_mb=round(output_size, 2)
            )
            
            return [output_file]
            
        except Exception as e:
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF organization: {str(e)}")
    
    async def _organize_pdf(
        self,
        input_file: str,
        output_file: str,
        operation: str,
        page_spec: str,
        preserve_bookmarks: bool
    ) -> Dict[str, Any]:
        """Organize a PDF file according to the specified operation."""
        try:
            # Read the input PDF
            with open(input_file, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                # Check if PDF is encrypted
                if pdf_reader.is_encrypted:
                    raise ProcessingError("Cannot organize encrypted PDF")
                
                original_pages = len(pdf_reader.pages)
                
                # Determine page sequence based on operation
                if operation == "reorder":
                    page_sequence = self._parse_reorder_spec(page_spec, original_pages)
                elif operation == "remove":
                    page_sequence = self._get_pages_after_removal(page_spec, original_pages)
                elif operation == "duplicate":
                    page_sequence = self._get_pages_with_duplicates(page_spec, original_pages)
                elif operation == "reverse":
                    page_sequence = list(range(original_pages, 0, -1))
                elif operation == "extract_odd":
                    page_sequence = list(range(1, original_pages + 1, 2))
                elif operation == "extract_even":
                    page_sequence = list(range(2, original_pages + 1, 2))
                elif operation == "interleave":
                    page_sequence = self._get_interleaved_pages(page_spec, original_pages)
                elif operation == "custom":
                    page_sequence = self._parse_custom_spec(page_spec, original_pages)
                else:
                    raise ValidationError(f"Unsupported operation: {operation}")
                
                # Create PDF writer
                pdf_writer = PdfWriter()
                
                # Add pages according to sequence
                for page_num in page_sequence:
                    if 1 <= page_num <= original_pages:
                        page = pdf_reader.pages[page_num - 1]  # Convert to 0-based index
                        pdf_writer.add_page(page)
                    else:
                        self.logger.warning(f"Invalid page number {page_num} skipped")
                
                # Handle bookmarks if requested
                if preserve_bookmarks and pdf_reader.outline:
                    try:
                        # This is a simplified bookmark preservation
                        # In a full implementation, you'd need to adjust bookmark page references
                        self.logger.info("Bookmark preservation requested but may need page reference adjustment")
                    except Exception as e:
                        self.logger.warning("Failed to preserve bookmarks", error=str(e))
                
                # Copy metadata if available
                if pdf_reader.metadata:
                    pdf_writer.add_metadata(pdf_reader.metadata)
                
                # Write the organized PDF
                with open(output_file, 'wb') as output_pdf:
                    pdf_writer.write(output_pdf)
                
                return {
                    "original_pages": original_pages,
                    "final_pages": len(page_sequence),
                    "page_sequence": page_sequence,
                    "operation": operation
                }
                
        except Exception as e:
            raise ProcessingError(f"Failed to organize PDF: {str(e)}")
    
    def _parse_reorder_spec(self, page_spec: str, total_pages: int) -> List[int]:
        """Parse reorder specification like '3,1,4,2' or '1-3,5,4'."""
        if not page_spec:
            raise ValidationError("Page specification required for reorder operation")
        
        page_sequence = []
        
        for part in page_spec.split(','):
            part = part.strip()
            
            if '-' in part:
                # Range like "1-3"
                start_str, end_str = part.split('-', 1)
                start = int(start_str.strip())
                end = int(end_str.strip())
                
                if start < 1 or end > total_pages or start > end:
                    raise ValidationError(f"Invalid page range: {part}")
                
                page_sequence.extend(range(start, end + 1))
            else:
                # Single page like "3"
                page = int(part)
                
                if page < 1 or page > total_pages:
                    raise ValidationError(f"Invalid page number: {page}")
                
                page_sequence.append(page)
        
        return page_sequence
    
    def _get_pages_after_removal(self, page_spec: str, total_pages: int) -> List[int]:
        """Get page sequence after removing specified pages."""
        if not page_spec:
            raise ValidationError("Page specification required for remove operation")
        
        # Parse pages to remove
        pages_to_remove = set()
        
        for part in page_spec.split(','):
            part = part.strip()
            
            if '-' in part:
                start_str, end_str = part.split('-', 1)
                start = int(start_str.strip())
                end = int(end_str.strip())
                
                if start < 1 or end > total_pages or start > end:
                    raise ValidationError(f"Invalid page range: {part}")
                
                pages_to_remove.update(range(start, end + 1))
            else:
                page = int(part)
                
                if page < 1 or page > total_pages:
                    raise ValidationError(f"Invalid page number: {page}")
                
                pages_to_remove.add(page)
        
        # Return all pages except those to be removed
        return [p for p in range(1, total_pages + 1) if p not in pages_to_remove]
    
    def _get_pages_with_duplicates(self, page_spec: str, total_pages: int) -> List[int]:
        """Get page sequence with specified pages duplicated."""
        if not page_spec:
            raise ValidationError("Page specification required for duplicate operation")
        
        # Start with all original pages
        page_sequence = list(range(1, total_pages + 1))
        
        # Parse pages to duplicate
        pages_to_duplicate = []
        
        for part in page_spec.split(','):
            part = part.strip()
            
            if '-' in part:
                start_str, end_str = part.split('-', 1)
                start = int(start_str.strip())
                end = int(end_str.strip())
                
                if start < 1 or end > total_pages or start > end:
                    raise ValidationError(f"Invalid page range: {part}")
                
                pages_to_duplicate.extend(range(start, end + 1))
            else:
                page = int(part)
                
                if page < 1 or page > total_pages:
                    raise ValidationError(f"Invalid page number: {page}")
                
                pages_to_duplicate.append(page)
        
        # Add duplicates at the end
        page_sequence.extend(pages_to_duplicate)
        
        return page_sequence
    
    def _get_interleaved_pages(self, page_spec: str, total_pages: int) -> List[int]:
        """Get interleaved pages from two sections."""
        if not page_spec:
            raise ValidationError("Page specification required for interleave operation (format: 'start1-end1,start2-end2')")
        
        parts = page_spec.split(',')
        if len(parts) != 2:
            raise ValidationError("Interleave requires exactly two page ranges")
        
        # Parse first range
        range1_str = parts[0].strip()
        if '-' not in range1_str:
            raise ValidationError("First range must be in format 'start-end'")
        
        start1_str, end1_str = range1_str.split('-', 1)
        start1 = int(start1_str.strip())
        end1 = int(end1_str.strip())
        
        # Parse second range
        range2_str = parts[1].strip()
        if '-' not in range2_str:
            raise ValidationError("Second range must be in format 'start-end'")
        
        start2_str, end2_str = range2_str.split('-', 1)
        start2 = int(start2_str.strip())
        end2 = int(end2_str.strip())
        
        # Validate ranges
        if start1 < 1 or end1 > total_pages or start1 > end1:
            raise ValidationError(f"Invalid first range: {range1_str}")
        
        if start2 < 1 or end2 > total_pages or start2 > end2:
            raise ValidationError(f"Invalid second range: {range2_str}")
        
        # Create interleaved sequence
        range1_pages = list(range(start1, end1 + 1))
        range2_pages = list(range(start2, end2 + 1))
        
        interleaved = []
        max_len = max(len(range1_pages), len(range2_pages))
        
        for i in range(max_len):
            if i < len(range1_pages):
                interleaved.append(range1_pages[i])
            if i < len(range2_pages):
                interleaved.append(range2_pages[i])
        
        return interleaved

    def _parse_custom_spec(self, page_spec: str, total_pages: int) -> List[int]:
        """Parse custom specification with multiple operations."""
        if not page_spec:
            raise ValidationError("Page specification required for custom operation")

        # Custom spec format: "operation1:pages1;operation2:pages2"
        # Example: "keep:1-5,10;duplicate:3;reverse:6-9"

        page_sequence = list(range(1, total_pages + 1))  # Start with all pages

        operations = page_spec.split(';')

        for op_spec in operations:
            if ':' not in op_spec:
                raise ValidationError(f"Invalid custom operation format: {op_spec}")

            op_name, op_pages = op_spec.split(':', 1)
            op_name = op_name.strip().lower()
            op_pages = op_pages.strip()

            if op_name == "keep":
                # Keep only specified pages
                keep_pages = self._parse_reorder_spec(op_pages, total_pages)
                page_sequence = [p for p in page_sequence if p in keep_pages]
            elif op_name == "remove":
                # Remove specified pages
                remove_pages = set(self._parse_reorder_spec(op_pages, total_pages))
                page_sequence = [p for p in page_sequence if p not in remove_pages]
            elif op_name == "duplicate":
                # Duplicate specified pages
                dup_pages = self._parse_reorder_spec(op_pages, total_pages)
                page_sequence.extend(dup_pages)
            else:
                raise ValidationError(f"Unsupported custom operation: {op_name}")

        return page_sequence

    def get_organization_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available organization options."""
        return {
            "operation": {
                "description": "Organization operation to perform",
                "type": "string",
                "options": list(self.operations.keys()),
                "default": "reorder",
                "operation_descriptions": self.operations
            },
            "page_spec": {
                "description": "Page specification for the operation",
                "type": "string",
                "examples": {
                    "reorder": "3,1,4,2 or 1-3,5,4",
                    "remove": "2,4-6,8",
                    "duplicate": "1,3,5",
                    "interleave": "1-5,10-14",
                    "custom": "keep:1-5,10;duplicate:3;remove:7"
                }
            },
            "preserve_bookmarks": {
                "description": "Attempt to preserve bookmarks (may need adjustment)",
                "type": "boolean",
                "default": False
            }
        }

    def get_available_operations(self) -> Dict[str, str]:
        """Get available organization operations."""
        return self.operations.copy()

    async def preview_organization(
        self,
        total_pages: int,
        operation: str,
        page_spec: str
    ) -> Dict[str, Any]:
        """Preview organization without actually performing it."""
        try:
            if operation == "reorder":
                page_sequence = self._parse_reorder_spec(page_spec, total_pages)
            elif operation == "remove":
                page_sequence = self._get_pages_after_removal(page_spec, total_pages)
            elif operation == "duplicate":
                page_sequence = self._get_pages_with_duplicates(page_spec, total_pages)
            elif operation == "reverse":
                page_sequence = list(range(total_pages, 0, -1))
            elif operation == "extract_odd":
                page_sequence = list(range(1, total_pages + 1, 2))
            elif operation == "extract_even":
                page_sequence = list(range(2, total_pages + 1, 2))
            elif operation == "interleave":
                page_sequence = self._get_interleaved_pages(page_spec, total_pages)
            elif operation == "custom":
                page_sequence = self._parse_custom_spec(page_spec, total_pages)
            else:
                raise ValidationError(f"Unsupported operation: {operation}")

            return {
                "operation": operation,
                "page_spec": page_spec,
                "original_pages": total_pages,
                "final_pages": len(page_sequence),
                "page_sequence": page_sequence[:20],  # Show first 20 pages
                "sequence_truncated": len(page_sequence) > 20,
                "is_valid": True,
                "changes": {
                    "pages_added": len(page_sequence) - total_pages,
                    "pages_removed": total_pages - len(set(page_sequence)),
                    "pages_duplicated": len(page_sequence) - len(set(page_sequence))
                }
            }

        except Exception as e:
            return {
                "operation": operation,
                "page_spec": page_spec,
                "original_pages": total_pages,
                "error": str(e),
                "is_valid": False
            }

    async def get_pdf_page_summary(self, file_path: str) -> Dict[str, Any]:
        """Get summary of PDF pages for organization planning."""
        try:
            with open(file_path, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)

                if pdf_reader.is_encrypted:
                    return {
                        "filename": os.path.basename(file_path),
                        "error": "PDF is encrypted and cannot be analyzed"
                    }

                total_pages = len(pdf_reader.pages)

                # Get basic page information
                page_info = []
                for page_num in range(min(total_pages, 10)):  # First 10 pages
                    page = pdf_reader.pages[page_num]

                    # Get page dimensions
                    width = float(page.mediabox.width)
                    height = float(page.mediabox.height)

                    # Determine orientation
                    orientation = "landscape" if width > height else "portrait"

                    # Try to get some text content
                    try:
                        text = page.extract_text()
                        has_text = bool(text.strip())
                        text_length = len(text.strip())
                    except Exception:
                        has_text = False
                        text_length = 0

                    page_info.append({
                        "page": page_num + 1,
                        "width": round(width, 2),
                        "height": round(height, 2),
                        "orientation": orientation,
                        "has_text": has_text,
                        "text_length": text_length
                    })

                # Check for bookmarks
                has_bookmarks = bool(pdf_reader.outline)

                return {
                    "filename": os.path.basename(file_path),
                    "total_pages": total_pages,
                    "size_mb": round(self.get_file_size_mb(file_path), 2),
                    "has_bookmarks": has_bookmarks,
                    "page_info": page_info,
                    "sample_note": f"Showing first {len(page_info)} pages" if total_pages > 10 else None,
                    "organization_suggestions": self._get_organization_suggestions(total_pages, page_info)
                }

        except Exception as e:
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }

    def _get_organization_suggestions(self, total_pages: int, page_info: List[Dict[str, Any]]) -> List[str]:
        """Get organization suggestions based on PDF analysis."""
        suggestions = []

        if total_pages > 50:
            suggestions.append("Large document - consider splitting into sections")

        # Check for mixed orientations
        orientations = [p["orientation"] for p in page_info]
        if len(set(orientations)) > 1:
            suggestions.append("Mixed page orientations detected - consider organizing by orientation")

        # Check for pages without text (possibly images/scans)
        text_pages = [p for p in page_info if p["has_text"]]
        if len(text_pages) < len(page_info) * 0.8:
            suggestions.append("Some pages appear to be images - consider separating text and image pages")

        if total_pages % 2 == 0:
            suggestions.append("Even number of pages - suitable for duplex printing organization")

        return suggestions


# Create tool instance
organize_pdf_tool = PDFOrganizeTool()
