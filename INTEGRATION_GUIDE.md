# PDF Processing Platform - Frontend-Backend Integration Guide

## Overview

This guide explains how the React frontend connects to the Python backend for the complete PDF processing platform with all 27 tools.

## Architecture

### Backend (Python FastAPI)
- **Location**: `backend/`
- **Framework**: FastAPI with async support
- **Features**: 27 PDF processing tools, session management, multi-user support
- **API Base URL**: `http://localhost:8000`

### Frontend (React + TypeScript)
- **Location**: `src/`
- **Framework**: React 18 with TypeScript
- **State Management**: Context API + Custom Hooks
- **API Integration**: Custom API client with session management

## Key Integration Components

### 1. API Service Layer (`src/services/`)

#### `api.ts`
- Main API client for all 27 PDF tools
- Handles file uploads, form data, and responses
- Includes timeout and error handling
- Type-safe interfaces for all operations

#### `sessionManager.ts`
- Manages user sessions and task tracking
- Handles task status polling
- Provides file download functionality
- Persists session state in localStorage

### 2. React Context (`src/contexts/`)

#### `SessionContext.tsx`
- Provides session state across the application
- Manages active tasks and their status
- Handles session creation and cleanup

### 3. Custom Hooks (`src/hooks/`)

#### `usePDFProcessor.ts`
- Unified interface for all 27 PDF tools
- Handles processing state and progress
- Provides callbacks for progress, completion, and errors

## API Endpoints

### Session Management
- `POST /api/sessions` - Create new session
- `GET /api/sessions/{session_id}/status` - Get session status

### Task Management
- `GET /api/tasks/{task_id}/status` - Get task status
- `GET /api/tasks/{task_id}/download/{filename}` - Download file

### PDF Processing Tools (27 endpoints)

#### Core Operations
- `POST /api/tools/merge` - Merge PDFs
- `POST /api/tools/compress` - Compress PDFs
- `POST /api/tools/split` - Split PDFs
- `POST /api/tools/rotate` - Rotate pages
- `POST /api/tools/organize` - Organize pages
- `POST /api/tools/crop` - Crop pages

#### Security & Protection
- `POST /api/tools/protect` - Password protect
- `POST /api/tools/unlock` - Remove password
- `POST /api/tools/redact` - Remove sensitive data
- `POST /api/tools/sign` - Digital signatures

#### Content Processing
- `POST /api/tools/ocr` - OCR text extraction
- `POST /api/tools/repair` - Fix corrupted PDFs
- `POST /api/tools/watermark` - Add watermarks
- `POST /api/tools/page-numbers` - Add page numbers
- `POST /api/tools/edit` - Edit PDF content

#### Format Conversion
- `POST /api/tools/convert/pdf-to-word` - PDF → Word
- `POST /api/tools/convert/pdf-to-image` - PDF → Images
- `POST /api/tools/convert/image-to-pdf` - Images → PDF
- `POST /api/tools/convert/word-to-pdf` - Word → PDF
- `POST /api/tools/convert/excel-to-pdf` - Excel → PDF
- `POST /api/tools/convert/html-to-pdf` - HTML → PDF
- `POST /api/tools/convert/pdf-to-excel` - PDF → Excel
- `POST /api/tools/convert/powerpoint-to-pdf` - PowerPoint → PDF
- `POST /api/tools/convert/pdf-to-powerpoint` - PDF → PowerPoint
- `POST /api/tools/convert/pdf-to-pdfa` - PDF → PDF/A

#### Analysis & Comparison
- `POST /api/tools/compare` - Compare PDFs
- `POST /api/tools/scan-to-pdf` - Scan to PDF

## Usage Example

### 1. Basic Tool Usage

```typescript
import { usePDFProcessor } from '../hooks/usePDFProcessor';

const MyComponent = () => {
  const { mergePDF, isProcessing, progress, error, outputFiles } = usePDFProcessor();

  const handleMerge = async (files: File[]) => {
    try {
      await mergePDF(files, {
        add_bookmarks: true,
        bookmark_titles: files.map(f => f.name)
      }, {
        onProgress: (progress, message) => {
          console.log(`${progress}%: ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Files ready:', outputFiles);
        }
      });
    } catch (error) {
      console.error('Processing failed:', error);
    }
  };

  return (
    <div>
      {isProcessing && <div>Progress: {progress}%</div>}
      {error && <div>Error: {error}</div>}
      {outputFiles && <div>Success! {outputFiles.length} files ready</div>}
    </div>
  );
};
```

### 2. Session Management

```typescript
import { useSession } from '../contexts/SessionContext';

const MyComponent = () => {
  const { 
    session, 
    activeTasks, 
    completedTasks, 
    downloadAllFiles 
  } = useSession();

  const handleDownload = async (taskId: string) => {
    try {
      await downloadAllFiles(taskId);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  return (
    <div>
      <div>Session: {session?.sessionId}</div>
      <div>Active tasks: {activeTasks.length}</div>
      <div>Completed tasks: {completedTasks.length}</div>
    </div>
  );
};
```

## Setup Instructions

### 1. Backend Setup
```bash
cd backend
pip install -r requirements.txt
cp .env.example .env
# Edit .env with your settings
python run.py
```

### 2. Frontend Setup
```bash
# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with API_BASE_URL

# Start development server
npm run dev
```

### 3. Environment Configuration

#### Backend `.env`
```
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///./pdf_tools.db
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

#### Frontend `.env`
```
VITE_API_BASE_URL=http://localhost:8000
VITE_NODE_ENV=development
```

## Error Handling

### API Errors
- Network timeouts (5 minutes default)
- HTTP status errors with detailed messages
- File upload validation errors

### Session Errors
- Automatic session renewal on expiration
- Task polling failure recovery
- Download error handling

### Processing Errors
- Tool-specific error messages
- Progress tracking interruption handling
- File cleanup on failures

## Performance Considerations

### File Upload
- Chunked uploads for large files
- Progress tracking during upload
- File type and size validation

### Task Processing
- Async processing with status polling
- Configurable timeout limits
- Background task management

### Session Management
- Automatic cleanup of expired sessions
- localStorage persistence
- Memory-efficient task tracking

## Security Features

### Authentication
- JWT token support (optional)
- Session-based isolation
- Rate limiting protection

### File Security
- Temporary file cleanup
- Session-scoped file access
- Secure download links

### Input Validation
- File type validation
- Parameter sanitization
- Size limit enforcement

## Monitoring & Debugging

### Logging
- Structured logging with correlation IDs
- Error tracking and reporting
- Performance metrics

### Development Tools
- API endpoint testing
- Session state inspection
- Task status monitoring

## Production Deployment

### Backend
- Docker containerization
- Environment-specific configuration
- Health check endpoints
- Monitoring and alerting

### Frontend
- Build optimization
- CDN deployment
- Environment variable management
- Error boundary implementation

## Troubleshooting

### Common Issues
1. **CORS errors**: Check backend CORS_ORIGINS setting
2. **Session timeouts**: Verify session expiration handling
3. **File upload failures**: Check file size limits and formats
4. **Processing timeouts**: Adjust timeout settings for large files

### Debug Steps
1. Check browser network tab for API calls
2. Verify backend logs for processing errors
3. Test API endpoints directly with curl/Postman
4. Check session state in browser localStorage

## Next Steps

1. **Testing**: Implement comprehensive integration tests
2. **Monitoring**: Set up production monitoring and alerting
3. **Performance**: Optimize for large file processing
4. **Features**: Add real-time progress updates via WebSocket
5. **Security**: Implement comprehensive authentication system
