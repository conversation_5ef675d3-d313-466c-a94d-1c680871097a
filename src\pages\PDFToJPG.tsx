import React, { useState } from 'react';
import { FileImage, Download, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const PDFToJPG = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [outputFormat, setOutputFormat] = useState<'jpg' | 'png'>('jpg');
  const [dpi, setDpi] = useState(300);
  const [pages, setPages] = useState('');
  const { downloadAllFiles } = useSession();

  const {
    isProcessing,
    progress,
    message,
    error,
    outputFiles,
    taskId,
    convertPDFToImage,
    resetState
  } = usePDFProcessor();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleConvert = async () => {
    if (files.length === 0) return;

    try {
      const parameters = {
        output_format: outputFormat,
        dpi: dpi,
        pages: pages || undefined,
      };

      await convertPDFToImage(files, parameters, {
        onProgress: (progress, message) => {
          console.log(`Progress: ${progress}% - ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Conversion completed:', outputFiles);
        },
        onError: (error) => {
          console.error('Conversion failed:', error);
        }
      });
    } catch (err) {
      console.error('Conversion operation failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="PDF en JPG"
      description="Extraire toutes les images contenues dans un fichier PDF ou convertir chaque page dans un fichier JPG"
      icon={<FileImage className="w-8 h-8" />}
      color="from-pink-500 to-pink-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de conversion
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Qualité d'image
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="quality"
                      value="low"
                      checked={quality === 'low'}
                      onChange={(e) => setQuality(e.target.value as 'low')}
                      className="text-pink-600"
                    />
                    <span className="text-slate-700">Faible (plus petite taille)</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="quality"
                      value="medium"
                      checked={quality === 'medium'}
                      onChange={(e) => setQuality(e.target.value as 'medium')}
                      className="text-pink-600"
                    />
                    <span className="text-slate-700">Moyenne (recommandée)</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="quality"
                      value="high"
                      checked={quality === 'high'}
                      onChange={(e) => setQuality(e.target.value as 'high')}
                      className="text-pink-600"
                    />
                    <span className="text-slate-700">Haute (meilleure qualité)</span>
                  </label>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="extract-images"
                  className="text-pink-600 rounded"
                />
                <label htmlFor="extract-images" className="text-slate-700">
                  Extraire uniquement les images existantes
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="convert-pages"
                  defaultChecked
                  className="text-pink-600 rounded"
                />
                <label htmlFor="convert-pages" className="text-slate-700">
                  Convertir chaque page en image
                </label>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-pink-600 to-rose-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en JPG</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default PDFToJPG;