import React, { useState } from 'react';
import { FileText, Download, ArrowRight, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const PDFToExcel = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [outputFormat, setOutputFormat] = useState<'xlsx' | 'xls'>('xlsx');
  const [extractionMethod, setExtractionMethod] = useState<'auto' | 'table' | 'text'>('auto');
  const [pages, setPages] = useState('');
  const [includeHeaders, setIncludeHeaders] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { downloadAllFiles } = useSession();

  const {
    isProcessing,
    progress,
    message,
    error,
    outputFiles,
    taskId,
    convertPDFToExcel,
    resetState
  } = usePDFProcessor();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleConvert = async () => {
    if (files.length === 0) return;

    try {
      const parameters = {
        output_format: outputFormat,
        extraction_method: extractionMethod,
        pages: pages || undefined,
        include_headers: includeHeaders,
      };

      await convertPDFToExcel(files, parameters, {
        onProgress: (progress, message) => {
          console.log(`Progress: ${progress}% - ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Conversion completed:', outputFiles);
        },
        onError: (error) => {
          console.error('Conversion failed:', error);
        }
      });
    } catch (err) {
      console.error('Conversion operation failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="PDF en Excel"
      description="Transférez les données de fichiers PDF vers des feuilles de calcul Excel en quelques secondes"
      icon={<FileText className="w-8 h-8" />}
      color="from-emerald-500 to-emerald-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-700">
                Options de conversion
              </h3>
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center space-x-2 text-emerald-600 hover:text-emerald-700 transition-colors"
              >
                <Settings className="w-4 h-4" />
                <span className="text-sm">Options avancées</span>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Format de sortie:
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="format"
                      value="xlsx"
                      checked={outputFormat === 'xlsx'}
                      onChange={(e) => setOutputFormat(e.target.value as any)}
                      className="text-emerald-600"
                    />
                    <span className="text-sm text-slate-700">XLSX (recommandé)</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="format"
                      value="xls"
                      checked={outputFormat === 'xls'}
                      onChange={(e) => setOutputFormat(e.target.value as any)}
                      className="text-emerald-600"
                    />
                    <span className="text-sm text-slate-700">XLS (ancien format)</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Méthode d'extraction:
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="extraction"
                      value="auto"
                      checked={extractionMethod === 'auto'}
                      onChange={(e) => setExtractionMethod(e.target.value as any)}
                      className="text-emerald-600"
                    />
                    <span className="text-sm text-slate-700">Automatique</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="extraction"
                      value="table"
                      checked={extractionMethod === 'table'}
                      onChange={(e) => setExtractionMethod(e.target.value as any)}
                      className="text-emerald-600"
                    />
                    <span className="text-sm text-slate-700">Tableaux uniquement</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="extraction"
                      value="text"
                      checked={extractionMethod === 'text'}
                      onChange={(e) => setExtractionMethod(e.target.value as any)}
                      className="text-emerald-600"
                    />
                    <span className="text-sm text-slate-700">Texte structuré</span>
                  </label>
                </div>
              </div>

              {showAdvanced && (
                <div className="space-y-3 pt-3 border-t border-slate-200">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      Pages spécifiques (optionnel):
                    </label>
                    <input
                      type="text"
                      placeholder="ex: 1-3,5,7 ou laissez vide pour toutes"
                      value={pages}
                      onChange={(e) => setPages(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="includeHeaders"
                      checked={includeHeaders}
                      onChange={(e) => setIncludeHeaders(e.target.checked)}
                      className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                    />
                    <label htmlFor="includeHeaders" className="text-sm text-slate-700">
                      Inclure les en-têtes de colonnes
                    </label>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-emerald-50 border border-emerald-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-emerald-600"></div>
              <span className="text-emerald-800 font-medium">Conversion en cours...</span>
            </div>
            <div className="w-full bg-emerald-200 rounded-full h-2 mb-2">
              <div
                className="bg-emerald-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-emerald-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la conversion</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Conversion réussie!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) Excel généré(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {/* Convert Button */}
        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-emerald-600 to-green-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <FileText className="w-5 h-5" />
                  <span>Convertir en Excel</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default PDFToExcel;