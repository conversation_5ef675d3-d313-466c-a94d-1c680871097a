import { useState } from 'react';
import { FolderOpen, Download, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const OrganizePDF = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    organizePDF, resetState
  } = usePDFProcessor();
  
  const [files, setFiles] = useState<File[]>([]);
  const [operation, setOperation] = useState<'reorder' | 'remove' | 'extract'>('reorder');
  const [pageOrder, setPageOrder] = useState<string>('');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleOrganize = async () => {
    if (files.length === 0) return;
    
    try {
      await organizePDF(files, {
        operation,
        page_order: pageOrder
      });
    } catch (err) {
      console.error('PDF organization failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Organiser PDF"
      description="Triez les pages de votre fichier PDF comme bon vous semble. Supprimez ou ajoutez des pages PDF à votre document"
      icon={<FolderOpen className="w-8 h-8" />}
      color="from-stone-500 to-stone-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Outils d'organisation disponibles
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">📄</span>
                </div>
                <span className="text-sm text-slate-700">Réorganiser</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">🗑️</span>
                </div>
                <span className="text-sm text-slate-700">Supprimer</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">➕</span>
                </div>
                <span className="text-sm text-slate-700">Ajouter</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">🔄</span>
                </div>
                <span className="text-sm text-slate-700">Dupliquer</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">✂️</span>
                </div>
                <span className="text-sm text-slate-700">Extraire</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">🔃</span>
                </div>
                <span className="text-sm text-slate-700">Remplacer</span>
              </div>
            </div>

            <div className="mt-6 bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-blue-800 font-medium">Fonctionnalités</span>
              </div>
              <ul className="text-sm text-blue-700 mt-1 space-y-1">
                <li>• Glisser-déposer pour réorganiser les pages</li>
                <li>• Aperçu en temps réel des modifications</li>
                <li>• Suppression et ajout de pages faciles</li>
                <li>• Sauvegarde automatique des modifications</li>
              </ul>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-stone-50 border border-stone-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-stone-600"></div>
              <span className="text-stone-800 font-medium">Organisation en cours...</span>
            </div>
            <div className="w-full bg-stone-200 rounded-full h-2 mb-2">
              <div
                className="bg-stone-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-stone-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de l'organisation</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Organisation réussie!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) organisé(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {/* Organize Button */}
        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleOrganize}
              disabled={isProcessing}
              className="bg-gradient-to-r from-stone-600 to-slate-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Organisation en cours...</span>
                </>
              ) : (
                <>
                  <FolderOpen className="w-5 h-5" />
                  <span>Organiser le PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default OrganizePDF;