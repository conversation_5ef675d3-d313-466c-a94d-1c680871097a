import React, { useState } from 'react';
import { RotateCw, Download, ArrowRight, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const RotatePDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [rotation, setRotation] = useState<90 | 180 | 270>(90);
  const [pages, setPages] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { downloadAllFiles } = useSession();

  const {
    isProcessing,
    progress,
    message,
    error,
    outputFiles,
    taskId,
    rotatePDF,
    resetState
  } = usePDFProcessor();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleRotate = async () => {
    if (files.length === 0) return;

    try {
      const parameters = {
        rotation: rotation,
        pages: pages || undefined,
      };

      await rotatePDF(files, parameters, {
        onProgress: (progress, message) => {
          console.log(`Progress: ${progress}% - ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Rotation completed:', outputFiles);
        },
        onError: (error) => {
          console.error('Rotation failed:', error);
        }
      });
    } catch (err) {
      console.error('Rotation operation failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Faire pivoter PDF"
      description="Faites pivoter votre PDF comme vous le souhaitez. Tournez plusieurs fichiers PDF à la fois!"
      icon={<RotateCw className="w-8 h-8" />}
      color="from-red-500 to-red-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={true}
          maxFiles={10}
          title="Sélectionnez vos fichiers PDF"
          description="Glissez-déposez un ou plusieurs fichiers PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Angle de rotation
            </h3>
            
            <div className="grid grid-cols-3 gap-4">
              <label className="flex flex-col items-center p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <input
                  type="radio"
                  name="rotation"
                  value="90"
                  checked={rotation === 90}
                  onChange={(e) => setRotation(Number(e.target.value) as 90)}
                  className="text-red-600 mb-2"
                />
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-2">
                  <RotateCw className="w-6 h-6 text-red-600" />
                </div>
                <span className="text-slate-700 font-medium">90°</span>
                <span className="text-sm text-slate-500">Droite</span>
              </label>

              <label className="flex flex-col items-center p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <input
                  type="radio"
                  name="rotation"
                  value="180"
                  checked={rotation === 180}
                  onChange={(e) => setRotation(Number(e.target.value) as 180)}
                  className="text-red-600 mb-2"
                />
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-2">
                  <div className="transform rotate-180">
                    <RotateCw className="w-6 h-6 text-red-600" />
                  </div>
                </div>
                <span className="text-slate-700 font-medium">180°</span>
                <span className="text-sm text-slate-500">Retournement</span>
              </label>

              <label className="flex flex-col items-center p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <input
                  type="radio"
                  name="rotation"
                  value="270"
                  checked={rotation === 270}
                  onChange={(e) => setRotation(Number(e.target.value) as 270)}
                  className="text-red-600 mb-2"
                />
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-2">
                  <div className="transform -rotate-90">
                    <RotateCw className="w-6 h-6 text-red-600" />
                  </div>
                </div>
                <span className="text-slate-700 font-medium">270°</span>
                <span className="text-sm text-slate-500">Gauche</span>
              </label>
            </div>

            <div className="mt-6 space-y-3">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="all-pages"
                  defaultChecked
                  className="text-red-600 rounded"
                />
                <label htmlFor="all-pages" className="text-slate-700">
                  Appliquer à toutes les pages
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="preserve-aspect"
                  defaultChecked
                  className="text-red-600 rounded"
                />
                <label htmlFor="preserve-aspect" className="text-slate-700">
                  Préserver les proportions
                </label>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleRotate}
              disabled={isProcessing}
              className="bg-gradient-to-r from-red-600 to-pink-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Rotation en cours...</span>
                </>
              ) : (
                <>
                  <span>Faire pivoter</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default RotatePDF;