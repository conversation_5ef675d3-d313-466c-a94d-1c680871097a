import React, { useState } from 'react';
import { FileText, Download, ArrowRight, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const WordToPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [quality, setQuality] = useState<'standard' | 'high' | 'maximum'>('high');
  const [preserveLayout, setPreserveLayout] = useState(true);
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { downloadAllFiles } = useSession();

  const {
    isProcessing,
    progress,
    message,
    error,
    outputFiles,
    taskId,
    convertWordToPDF,
    resetState
  } = usePDFProcessor();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleConvert = async () => {
    if (files.length === 0) return;

    try {
      const parameters = {
        quality: quality,
        preserve_layout: preserveLayout,
        include_metadata: includeMetadata,
      };

      await convertWordToPDF(files, parameters, {
        onProgress: (progress, message) => {
          console.log(`Progress: ${progress}% - ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Conversion completed:', outputFiles);
        },
        onError: (error) => {
          console.error('Conversion failed:', error);
        }
      });
    } catch (err) {
      console.error('Conversion operation failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Word en PDF"
      description="Convertir vos documents dans un fichier PDF qui est exactement le même que le DOC ou DOCX original"
      icon={<FileText className="w-8 h-8" />}
      color="from-sky-500 to-sky-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".doc,.docx"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre document Word"
          description="Glissez-déposez un fichier DOC ou DOCX ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-700">
                Options de conversion
              </h3>
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center space-x-2 text-sky-600 hover:text-sky-700 transition-colors"
              >
                <Settings className="w-4 h-4" />
                <span className="text-sm">Options avancées</span>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Qualité de conversion:
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="quality"
                      value="standard"
                      checked={quality === 'standard'}
                      onChange={(e) => setQuality(e.target.value as any)}
                      className="text-sky-600"
                    />
                    <span className="text-sm text-slate-700">Standard (plus rapide)</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="quality"
                      value="high"
                      checked={quality === 'high'}
                      onChange={(e) => setQuality(e.target.value as any)}
                      className="text-sky-600"
                    />
                    <span className="text-sm text-slate-700">Haute qualité (recommandé)</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="quality"
                      value="maximum"
                      checked={quality === 'maximum'}
                      onChange={(e) => setQuality(e.target.value as any)}
                      className="text-sky-600"
                    />
                    <span className="text-sm text-slate-700">Qualité maximale</span>
                  </label>
                </div>
              </div>

              {showAdvanced && (
                <div className="space-y-3 pt-3 border-t border-slate-200">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="preserveLayout"
                      checked={preserveLayout}
                      onChange={(e) => setPreserveLayout(e.target.checked)}
                      className="rounded border-gray-300 text-sky-600 focus:ring-sky-500"
                    />
                    <label htmlFor="preserveLayout" className="text-sm text-slate-700">
                      Préserver la mise en page originale
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="includeMetadata"
                      checked={includeMetadata}
                      onChange={(e) => setIncludeMetadata(e.target.checked)}
                      className="rounded border-gray-300 text-sky-600 focus:ring-sky-500"
                    />
                    <label htmlFor="includeMetadata" className="text-sm text-slate-700">
                      Inclure les métadonnées du document
                    </label>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-sky-50 border border-sky-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-sky-600"></div>
              <span className="text-sky-800 font-medium">Conversion en cours...</span>
            </div>
            <div className="w-full bg-sky-200 rounded-full h-2 mb-2">
              <div
                className="bg-sky-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-sky-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la conversion</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Conversion réussie!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) PDF généré(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {/* Convert Button */}
        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-sky-600 to-blue-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <FileText className="w-5 h-5" />
                  <span>Convertir en PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default WordToPDF;