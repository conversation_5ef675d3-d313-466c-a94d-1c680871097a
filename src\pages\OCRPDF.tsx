import { useState } from 'react';
import { Search, Download, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const OCRPDF = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    ocrPDF, resetState
  } = usePDFProcessor();
  
  const [files, setFiles] = useState<File[]>([]);
  const [language, setLanguage] = useState<'fr' | 'en' | 'es' | 'de' | 'auto'>('auto');
  const [outputFormat, setOutputFormat] = useState<'searchable' | 'text' | 'both'>('searchable');
  const [dpi, setDpi] = useState(300);
  const [preprocessing, setPreprocessing] = useState(true);
  const [confidenceThreshold, setConfidenceThreshold] = useState(75);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleOCR = async () => {
    if (files.length === 0) return;

    try {
      await ocrPDF(files, {
        language,
        output_format: outputFormat,
        dpi,
        preprocessing,
        confidence_threshold: confidenceThreshold
      });
    } catch (err) {
      console.error('OCR processing failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="OCR PDF"
      description="Convertissez en toute simplicité vos PDF numérisés en documents indexables et modifiables"
      icon={<Search className="w-8 h-8" />}
      color="from-blue-500 to-blue-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre PDF numérisé"
          description="Glissez-déposez un fichier PDF numérisé ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de reconnaissance
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Langue du document
                </label>
                <select 
                  value={language}
                  onChange={(e) => setLanguage(e.target.value as 'fr' | 'en' | 'es' | 'de' | 'auto')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="auto">Détection automatique</option>
                  <option value="fr">Français</option>
                  <option value="en">English</option>
                  <option value="es">Español</option>
                  <option value="de">Deutsch</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Format de sortie
                </label>
                <select 
                  value={outputFormat}
                  onChange={(e) => setOutputFormat(e.target.value as 'searchable' | 'text' | 'both')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="searchable">PDF consultable</option>
                  <option value="text">Texte uniquement</option>
                  <option value="both">PDF consultable + Texte</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Qualité de reconnaissance (DPI)
                </label>
                <select 
                  value={dpi}
                  onChange={(e) => setDpi(parseInt(e.target.value))}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={150}>150 DPI (Rapide)</option>
                  <option value={300}>300 DPI (Recommandé)</option>
                  <option value={600}>600 DPI (Haute qualité)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Seuil de confiance: {confidenceThreshold}%
                </label>
                <input
                  type="range"
                  min="50"
                  max="95"
                  value={confidenceThreshold}
                  onChange={(e) => setConfidenceThreshold(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>50% (Rapide)</span>
                  <span>95% (Précis)</span>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="preprocessing"
                    checked={preprocessing}
                    onChange={(e) => setPreprocessing(e.target.checked)}
                    className="text-blue-600 rounded"
                  />
                  <label htmlFor="preprocessing" className="text-slate-700">
                    Amélioration d'image automatique
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="preserve-layout"
                    defaultChecked
                    className="text-blue-600 rounded"
                  />
                  <label htmlFor="preserve-layout" className="text-slate-700">
                    Préserver la mise en page originale
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="extract-tables"
                    defaultChecked
                    className="text-blue-600 rounded"
                  />
                  <label htmlFor="extract-tables" className="text-slate-700">
                    Détecter et extraire les tableaux
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="searchable-text"
                    defaultChecked
                    className="text-blue-600 rounded"
                  />
                  <label htmlFor="searchable-text" className="text-slate-700">
                    Rendre le texte consultable
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="editable-text"
                    className="text-blue-600 rounded"
                  />
                  <label htmlFor="editable-text" className="text-slate-700">
                    Rendre le texte modifiable
                  </label>
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-800 font-medium">Fonctionnalités OCR</span>
                </div>
                <ul className="text-sm text-green-700 mt-1 space-y-1">
                  <li>• Reconnaissance de texte avec précision élevée</li>
                  <li>• Support de multiples langues</li>
                  <li>• Préservation de la mise en page</li>
                  <li>• Extraction de tableaux et formulaires</li>
                  <li>• Texte sélectionnable et copiable</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <span className="text-blue-800">Reconnaissance de texte en cours...</span>
            </div>
            {progress > 0 && (
              <div className="mt-3">
                <div className="bg-blue-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-blue-600 mt-1">{progress}% terminé</p>
              </div>
            )}
            {message && (
              <p className="text-sm text-blue-600 mt-2">{message}</p>
            )}
          </div>
        )}

        {/* Error Status */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-red-800">Erreur lors de la reconnaissance</h3>
                <p className="text-red-700 text-sm mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success Status */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-medium text-green-800">OCR appliqué avec succès!</h3>
                <p className="text-green-700 text-sm mt-1">
                  Votre document est maintenant indexable et le texte peut être sélectionné.
                </p>
                <div className="mt-3">
                  <button
                    onClick={handleDownload}
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Télécharger
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleOCR}
              disabled={isProcessing}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Reconnaissance en cours...</span>
                </>
              ) : (
                <>
                  <span>Appliquer l'OCR</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default OCRPDF;