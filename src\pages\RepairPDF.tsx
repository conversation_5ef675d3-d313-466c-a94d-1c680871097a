import { useState } from 'react';
import { Wrench, Download, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const RepairPDF = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    repairPDF, resetState
  } = usePDFProcessor();
  
  const [files, setFiles] = useState<File[]>([]);
  const [repairStrategy, setRepairStrategy] = useState<'standard' | 'aggressive' | 'conservative'>('standard');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleRepair = async () => {
    if (files.length === 0) return;

    try {
      await repairPDF(files, {
        repair_strategy: repairStrategy
      });
    } catch (err) {
      console.error('PDF repair failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Réparer PDF"
      description="Réparez un PDF endommagé et restaurez les données d'un PDF corrompu"
      icon={<Wrench className="w-8 h-8" />}
      color="from-neutral-500 to-neutral-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF endommagé"
          description="Glissez-déposez un fichier PDF corrompu ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Outils de réparation
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Stratégie de réparation
                </label>
                <select 
                  value={repairStrategy}
                  onChange={(e) => setRepairStrategy(e.target.value as 'standard' | 'aggressive' | 'conservative')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-neutral-500"
                >
                  <option value="conservative">Conservative (Sûre)</option>
                  <option value="standard">Standard (Recommandée)</option>
                  <option value="aggressive">Agressive (Récupération maximale)</option>
                </select>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="fix-structure"
                  defaultChecked
                  className="text-neutral-600 rounded"
                />
                <label htmlFor="fix-structure" className="text-slate-700">
                  Réparer la structure du fichier
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="recover-text"
                  defaultChecked
                  className="text-neutral-600 rounded"
                />
                <label htmlFor="recover-text" className="text-slate-700">
                  Récupérer le texte
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="recover-images"
                  defaultChecked
                  className="text-neutral-600 rounded"
                />
                <label htmlFor="recover-images" className="text-slate-700">
                  Récupérer les images
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="fix-fonts"
                  defaultChecked
                  className="text-neutral-600 rounded"
                />
                <label htmlFor="fix-fonts" className="text-slate-700">
                  Réparer les polices
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="optimize-after-repair"
                  className="text-neutral-600 rounded"
                />
                <label htmlFor="optimize-after-repair" className="text-slate-700">
                  Optimiser après réparation
                </label>
              </div>
            </div>

            <div className="mt-6 bg-orange-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-orange-800 font-medium">Problèmes courants détectés</span>
              </div>
              <ul className="text-sm text-orange-700 mt-1 space-y-1">
                <li>• Erreurs de structure du fichier</li>
                <li>• Références d'objets brisées</li>
                <li>• Corruption des polices</li>
                <li>• Problèmes de compression</li>
                <li>• Métadonnées corrompues</li>
              </ul>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <span className="text-blue-800">Réparation du PDF en cours...</span>
            </div>
            {progress > 0 && (
              <div className="mt-3">
                <div className="bg-blue-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-blue-600 mt-1">{progress}% terminé</p>
              </div>
            )}
            {message && (
              <p className="text-sm text-blue-600 mt-2">{message}</p>
            )}
          </div>
        )}

        {/* Error Status */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-red-800">Erreur lors de la réparation</h3>
                <p className="text-red-700 text-sm mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success Status */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-medium text-green-800">PDF réparé avec succès!</h3>
                <p className="text-green-700 text-sm mt-1">
                  Votre fichier PDF a été réparé et est maintenant fonctionnel.
                </p>
                <div className="mt-3">
                  <button
                    onClick={handleDownload}
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Télécharger
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleRepair}
              disabled={isProcessing}
              className="bg-gradient-to-r from-neutral-600 to-slate-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Réparation en cours...</span>
                </>
              ) : (
                <>
                  <span>Réparer le PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default RepairPDF;