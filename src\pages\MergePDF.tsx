import React, { useState } from 'react';
import { FileText, Download, ArrowR<PERSON>, Settings, CheckCircle, AlertCircle } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const MergePDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [bookmarkTitles, setBookmarkTitles] = useState<string[]>([]);
  const [addBookmarks, setAddBookmarks] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { t } = useLanguage();
  const { downloadAllFiles } = useSession();

  const {
    isProcessing,
    progress,
    message,
    error,
    outputFiles,
    mergePDF,
    resetState
  } = usePDFProcessor();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    // Initialize bookmark titles with file names
    setBookmarkTitles(selectedFiles.map(file => file.name.replace('.pdf', '')));
    resetState();
  };

  const handleBookmarkTitleChange = (index: number, title: string) => {
    const newTitles = [...bookmarkTitles];
    newTitles[index] = title;
    setBookmarkTitles(newTitles);
  };

  const handleMerge = async () => {
    if (files.length < 2) {
      console.error('Not enough files for merge:', files.length);
      return;
    }

    console.log('Starting merge process with files:', files.map(f => f.name));

    try {
      const parameters = {
        add_bookmarks: addBookmarks,
        bookmark_titles: addBookmarks ? bookmarkTitles : undefined,
      };

      console.log('Merge parameters:', parameters);

      await mergePDF(files, parameters, {
        onProgress: (progress, message) => {
          console.log(`Progress: ${progress}% - ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Merge completed:', outputFiles);
        },
        onError: (error) => {
          console.error('Merge failed:', error);
          alert(`Erreur lors de la fusion: ${error}`);
        }
      });
    } catch (err) {
      console.error('Merge operation failed:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      alert(`Erreur lors de la fusion: ${errorMessage}`);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0) {
      try {
        // Get the task ID from the processor state
        const taskId = (usePDFProcessor as any).taskId;
        if (taskId) {
          await downloadAllFiles(taskId);
        }
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title={t('tool.merge.title')}
      description={t('tool.merge.description')}
      icon={<FileText className="w-8 h-8" />}
      color="from-blue-500 to-blue-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={true}
          maxFiles={10}
          title="Sélectionnez vos fichiers PDF"
          description="Glissez-déposez plusieurs fichiers PDF ici ou cliquez pour sélectionner (max 10 fichiers)"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-700">
                Ordre de fusion ({files.length} fichiers)
              </h3>
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors"
              >
                <Settings className="w-4 h-4" />
                <span className="text-sm">Options avancées</span>
              </button>
            </div>

            {showAdvanced && (
              <div className="mb-4 p-4 bg-white rounded-lg border">
                <div className="flex items-center space-x-2 mb-3">
                  <input
                    type="checkbox"
                    id="addBookmarks"
                    checked={addBookmarks}
                    onChange={(e) => setAddBookmarks(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="addBookmarks" className="text-sm font-medium text-slate-700">
                    Ajouter des signets pour chaque fichier
                  </label>
                </div>

                {addBookmarks && (
                  <div className="space-y-2">
                    <p className="text-xs text-slate-600 mb-2">Personnaliser les titres des signets:</p>
                    {files.map((file, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <span className="text-xs text-slate-500 w-8">{index + 1}.</span>
                        <input
                          type="text"
                          value={bookmarkTitles[index] || ''}
                          onChange={(e) => handleBookmarkTitleChange(index, e.target.value)}
                          className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                          placeholder={file.name.replace('.pdf', '')}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            <div className="space-y-2">
              {files.map((file, index) => (
                <div key={index} className="flex items-center justify-between bg-white p-3 rounded-lg shadow-sm">
                  <div className="flex items-center space-x-3">
                    <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-sm font-medium">
                      {index + 1}
                    </span>
                    <FileText className="w-5 h-5 text-slate-500" />
                    <span className="text-sm font-medium text-slate-700">{file.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="text-slate-400 hover:text-blue-600 transition-colors">
                      ↑
                    </button>
                    <button className="text-slate-400 hover:text-blue-600 transition-colors">
                      ↓
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-blue-50 border border-blue-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <span className="text-blue-800 font-medium">Fusion en cours...</span>
            </div>
            <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-blue-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la fusion</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Fusion réussie!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) généré(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {/* Merge Button */}
        {files.length >= 2 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleMerge}
              disabled={isProcessing}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Fusion en cours...</span>
                </>
              ) : (
                <>
                  <span>{t('button.merge')}</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default MergePDF;