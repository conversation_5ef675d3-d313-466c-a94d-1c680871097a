/**
 * Session management service for handling user sessions and task tracking
 */

import { apiClient, SessionResponse } from './api';

export interface SessionInfo {
  sessionId: string;
  createdAt: string;
  lastActivity: string;
  activeTasks: number;
}

export interface TaskInfo {
  taskId: string;
  status: 'processing' | 'completed' | 'failed';
  toolName: string;
  createdAt: string;
  progress?: number;
  message?: string;
  outputFiles?: string[];
  errorMessage?: string;
  processingTime?: number;
}

class SessionManager {
  private currentSession: SessionInfo | null = null;
  private activeTasks: Map<string, TaskInfo> = new Map();
  private sessionStorageKey = 'pdf_tools_session';
  private tasksStorageKey = 'pdf_tools_tasks';

  constructor() {
    this.loadFromStorage();
  }

  // Session Management
  async createSession(): Promise<SessionInfo> {
    try {
      console.log('Creating new session...');
      const response: SessionResponse = await apiClient.createSession();
      console.log('Session created successfully:', response);

      this.currentSession = {
        sessionId: response.session_id,
        createdAt: response.created_at,
        lastActivity: response.last_activity,
        activeTasks: response.active_tasks,
      };

      this.saveToStorage();
      console.log('Session saved to storage:', this.currentSession);
      return this.currentSession;
    } catch (error) {
      console.error('Failed to create session:', error);
      throw new Error('Failed to create session. Please try again.');
    }
  }

  async ensureSession(): Promise<SessionInfo> {
    if (!this.currentSession || this.isSessionExpired()) {
      return await this.createSession();
    }
    
    return this.currentSession;
  }

  getCurrentSession(): SessionInfo | null {
    return this.currentSession;
  }

  isSessionExpired(): boolean {
    if (!this.currentSession) return true;

    // Check if session is older than 1 hour (60 minutes)
    const lastActivity = new Date(this.currentSession.lastActivity);
    const now = new Date();
    const hourInMs = 60 * 60 * 1000;

    return (now.getTime() - lastActivity.getTime()) > hourInMs;
  }

  clearSession(): void {
    this.currentSession = null;
    this.activeTasks.clear();
    this.clearStorage();
  }

  // Task Management
  addTask(taskId: string, toolName: string): TaskInfo {
    const taskInfo: TaskInfo = {
      taskId,
      status: 'processing',
      toolName,
      createdAt: new Date().toISOString(),
      progress: 0,
      message: 'Processing started...',
    };
    
    this.activeTasks.set(taskId, taskInfo);
    this.saveToStorage();
    
    return taskInfo;
  }

  updateTask(taskId: string, updates: Partial<TaskInfo>): TaskInfo | null {
    const task = this.activeTasks.get(taskId);
    if (!task) return null;
    
    const updatedTask = { ...task, ...updates };
    this.activeTasks.set(taskId, updatedTask);
    this.saveToStorage();
    
    return updatedTask;
  }

  getTask(taskId: string): TaskInfo | null {
    return this.activeTasks.get(taskId) || null;
  }

  getAllTasks(): TaskInfo[] {
    return Array.from(this.activeTasks.values());
  }

  getActiveTasks(): TaskInfo[] {
    return this.getAllTasks().filter(task => task.status === 'processing');
  }

  getCompletedTasks(): TaskInfo[] {
    return this.getAllTasks().filter(task => task.status === 'completed');
  }

  getFailedTasks(): TaskInfo[] {
    return this.getAllTasks().filter(task => task.status === 'failed');
  }

  removeTask(taskId: string): boolean {
    const removed = this.activeTasks.delete(taskId);
    if (removed) {
      this.saveToStorage();
    }
    return removed;
  }

  clearCompletedTasks(): void {
    const activeTasks = new Map();
    
    this.activeTasks.forEach((task, taskId) => {
      if (task.status === 'processing') {
        activeTasks.set(taskId, task);
      }
    });
    
    this.activeTasks = activeTasks;
    this.saveToStorage();
  }

  // Task Status Polling
  async pollTaskStatus(taskId: string, onUpdate?: (task: TaskInfo) => void): Promise<TaskInfo> {
    const pollInterval = 2000; // 2 seconds
    const maxAttempts = 150; // 5 minutes total
    let attempts = 0;

    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++;
          
          const response = await apiClient.getTaskStatus(taskId);
          
          const updatedTask = this.updateTask(taskId, {
            status: response.status,
            progress: response.progress,
            message: response.message,
            outputFiles: response.output_files,
            errorMessage: response.error_message,
            processingTime: response.processing_time,
          });
          
          if (updatedTask && onUpdate) {
            onUpdate(updatedTask);
          }
          
          if (response.status === 'completed' || response.status === 'failed') {
            resolve(updatedTask!);
            return;
          }
          
          if (attempts >= maxAttempts) {
            reject(new Error('Task polling timeout'));
            return;
          }
          
          setTimeout(poll, pollInterval);
        } catch (error) {
          console.error('Error polling task status:', error);
          
          if (attempts >= maxAttempts) {
            reject(error);
            return;
          }
          
          setTimeout(poll, pollInterval);
        }
      };
      
      poll();
    });
  }

  // File Download
  async downloadFile(taskId: string, filename: string): Promise<void> {
    try {
      const blob = await apiClient.downloadFile(taskId, filename);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download file:', error);
      throw new Error('Failed to download file. Please try again.');
    }
  }

  async downloadAllFiles(taskId: string): Promise<void> {
    const task = this.getTask(taskId);
    if (!task || !task.outputFiles || task.outputFiles.length === 0) {
      throw new Error('No files available for download');
    }

    // Download each file
    for (const filename of task.outputFiles) {
      await this.downloadFile(taskId, filename);
      // Small delay between downloads
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  // Storage Management
  private saveToStorage(): void {
    try {
      if (this.currentSession) {
        localStorage.setItem(this.sessionStorageKey, JSON.stringify(this.currentSession));
      }
      
      const tasksArray = Array.from(this.activeTasks.entries());
      localStorage.setItem(this.tasksStorageKey, JSON.stringify(tasksArray));
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  }

  private loadFromStorage(): void {
    try {
      // Load session
      const sessionData = localStorage.getItem(this.sessionStorageKey);
      if (sessionData) {
        this.currentSession = JSON.parse(sessionData);
        
        // Check if session is expired
        if (this.isSessionExpired()) {
          this.currentSession = null;
        }
      }
      
      // Load tasks
      const tasksData = localStorage.getItem(this.tasksStorageKey);
      if (tasksData) {
        const tasksArray = JSON.parse(tasksData);
        this.activeTasks = new Map(tasksArray);
      }
    } catch (error) {
      console.warn('Failed to load from localStorage:', error);
      this.clearStorage();
    }
  }

  private clearStorage(): void {
    try {
      localStorage.removeItem(this.sessionStorageKey);
      localStorage.removeItem(this.tasksStorageKey);
    } catch (error) {
      console.warn('Failed to clear localStorage:', error);
    }
  }

  // Utility Methods
  getSessionSummary(): {
    sessionActive: boolean;
    totalTasks: number;
    activeTasks: number;
    completedTasks: number;
    failedTasks: number;
  } {
    return {
      sessionActive: !!this.currentSession && !this.isSessionExpired(),
      totalTasks: this.activeTasks.size,
      activeTasks: this.getActiveTasks().length,
      completedTasks: this.getCompletedTasks().length,
      failedTasks: this.getFailedTasks().length,
    };
  }
}

// Create and export session manager instance
export const sessionManager = new SessionManager();

// Export default
export default sessionManager;
