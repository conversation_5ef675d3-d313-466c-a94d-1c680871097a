# PDF Processing Backend API

A FastAPI-based backend for multi-user concurrent PDF processing with session management and file isolation.

## Features

- **Multi-user Concurrency**: Handle multiple users processing PDFs simultaneously
- **Session Management**: Isolated temporary directories for each user session
- **File Security**: Secure file upload, validation, and automatic cleanup
- **Authentication**: JWT-based authentication with usage tracking
- **Rate Limiting**: Prevent abuse with configurable rate limits
- **Async Processing**: Non-blocking PDF processing with progress tracking
- **Comprehensive Logging**: Structured logging for monitoring and debugging

## Quick Start

### 1. Install Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 2. Set Environment Variables

Copy the example environment file and configure:

```bash
cp .env.example .env
# Edit .env with your settings
```

Required environment variables:
- `SECRET_KEY`: JWT secret key (generate a secure random string)
- `DATABASE_URL`: Database connection string
- `REDIS_URL`: Redis connection for task queue

### 3. Run the Development Server

```bash
python run.py
```

The API will be available at `http://localhost:8000`

### 4. API Documentation

Visit `http://localhost:8000/docs` for interactive API documentation.

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user info
- `GET /api/auth/usage` - Get usage statistics

### Session Management
- `POST /api/tools/session` - Create new processing session
- `GET /api/tools/session/{session_id}` - Get session info

### PDF Tools
- `POST /api/tools/merge` - Merge multiple PDFs
- `POST /api/tools/compress` - Compress PDF file
- `POST /api/tools/split` - Split PDF into multiple files
- `POST /api/tools/convert` - Convert between formats
- `GET /api/tools/task/{task_id}` - Get task status
- `GET /api/tools/download/{task_id}` - Download processed file

## Architecture

### Session Management
Each user gets an isolated session with:
- Unique session ID
- Temporary directory for file processing
- Automatic cleanup after timeout
- Concurrent task tracking

### File Processing
- Secure file upload with validation
- Virus scanning (configurable)
- Format validation and conversion
- Automatic cleanup of temporary files

### Security Features
- JWT authentication
- File type validation
- Size limits
- Rate limiting
- Session isolation
- Secure file handling

## Configuration

Key configuration options in `.env`:

```env
# Security
SECRET_KEY=your-secret-key
MAX_FILE_SIZE=52428800  # 50MB

# Performance
MAX_CONCURRENT_TASKS=10
TASK_TIMEOUT_MINUTES=30
SESSION_TIMEOUT_MINUTES=60

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_MINUTES=60
```

## Development

### Running Tests

```bash
pytest tests/
```

### Code Formatting

```bash
black app/
isort app/
flake8 app/
```

### Database Migrations

```bash
alembic revision --autogenerate -m "Description"
alembic upgrade head
```

## Production Deployment

### Using Docker

```bash
docker build -t pdf-api .
docker run -p 8000:8000 pdf-api
```

### Using Docker Compose

```bash
docker-compose up -d
```

### Environment Variables for Production

- Set `DEBUG=false`
- Use PostgreSQL for `DATABASE_URL`
- Configure Redis for task queue
- Set up proper logging
- Configure reverse proxy (nginx)
- Set up SSL certificates

## Monitoring

The API provides several monitoring endpoints:

- `/health` - Health check
- `/metrics` - Application metrics
- Structured logging with correlation IDs
- Task status tracking

## Troubleshooting

### Common Issues

1. **File upload fails**: Check `MAX_FILE_SIZE` and disk space
2. **Processing timeout**: Increase `TASK_TIMEOUT_MINUTES`
3. **Session cleanup**: Check `SESSION_TIMEOUT_MINUTES` setting
4. **Database errors**: Verify `DATABASE_URL` and run migrations

### Logs

Check application logs for detailed error information:

```bash
tail -f logs/app.log
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run code formatting and tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
