import { useState } from 'react';
import { PenTool, Download, ArrowR<PERSON>, CheckCircle, AlertCircle } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const SignPDF = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    signPDF, resetState
  } = usePDFProcessor();
  
  const [files, setFiles] = useState<File[]>([]);
  const [signatureType, setSignatureType] = useState<'text' | 'draw' | 'upload'>('text');
  const [signatureText, setSignatureText] = useState('');
  const [position, setPosition] = useState<'bottom-left' | 'bottom-right' | 'top-left' | 'top-right' | 'center'>('bottom-right');
  const [pages, setPages] = useState<string>('');
  const [signerName, setSignerName] = useState('');
  const [reason, setReason] = useState('');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleSign = async () => {
    if (files.length === 0) return;
    
    try {
      await signPDF(files, {
        signature_type: signatureType,
        signature_text: signatureText,
        position,
        pages: pages || undefined,
        signer_name: signerName,
        reason
      });
    } catch (err) {
      console.error('PDF signing failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Signer PDF"
      description="Signez vous-même ou demandez des signatures électroniques à des tiers"
      icon={<PenTool className="w-8 h-8" />}
      color="from-cyan-500 to-cyan-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de signature
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="my-signature"
                  defaultChecked
                  className="text-cyan-600 rounded"
                />
                <label htmlFor="my-signature" className="text-slate-700">
                  Ajouter ma signature
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="request-signature"
                  className="text-cyan-600 rounded"
                />
                <label htmlFor="request-signature" className="text-slate-700">
                  Demander une signature à un tiers
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="add-date"
                  className="text-cyan-600 rounded"
                />
                <label htmlFor="add-date" className="text-slate-700">
                  Ajouter la date automatiquement
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="add-text"
                  className="text-cyan-600 rounded"
                />
                <label htmlFor="add-text" className="text-slate-700">
                  Ajouter du texte personnalisé
                </label>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">Types de signatures disponibles</h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
                    <span className="text-blue-600">✏️</span>
                  </div>
                  <span className="text-sm text-blue-700">Dessinée</span>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
                    <span className="text-blue-600">📝</span>
                  </div>
                  <span className="text-sm text-blue-700">Tapée</span>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
                    <span className="text-blue-600">📁</span>
                  </div>
                  <span className="text-sm text-blue-700">Importée</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-cyan-50 border border-cyan-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-cyan-600"></div>
              <span className="text-cyan-800 font-medium">Signature en cours...</span>
            </div>
            <div className="w-full bg-cyan-200 rounded-full h-2 mb-2">
              <div
                className="bg-cyan-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-cyan-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la signature</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Signature réussie!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) signé(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {/* Sign Button */}
        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleSign}
              disabled={isProcessing}
              className="bg-gradient-to-r from-cyan-600 to-blue-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Signature en cours...</span>
                </>
              ) : (
                <>
                  <PenTool className="w-5 h-5" />
                  <span>Signer le PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default SignPDF;