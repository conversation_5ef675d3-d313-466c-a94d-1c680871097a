import React, { ReactNode } from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import ErrorBoundary from './ErrorBoundary';
import { useErrorHandler, ErrorInfo } from '../hooks/useErrorHandler';

interface PDFToolWrapperProps {
  children: ReactNode;
  onRetry?: () => void;
}

interface ErrorDisplayProps {
  error: ErrorInfo;
  onRetry?: () => void;
  onClear: () => void;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error, onRetry, onClear }) => {
  const getErrorColor = (type: string) => {
    switch (type) {
      case 'warning':
        return {
          bg: 'bg-yellow-50',
          border: 'border-yellow-200',
          icon: 'text-yellow-600',
          title: 'text-yellow-800',
          text: 'text-yellow-700',
          button: 'bg-yellow-600 hover:bg-yellow-700',
        };
      case 'info':
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          icon: 'text-blue-600',
          title: 'text-blue-800',
          text: 'text-blue-700',
          button: 'bg-blue-600 hover:bg-blue-700',
        };
      default:
        return {
          bg: 'bg-red-50',
          border: 'border-red-200',
          icon: 'text-red-600',
          title: 'text-red-800',
          text: 'text-red-700',
          button: 'bg-red-600 hover:bg-red-700',
        };
    }
  };

  const colors = getErrorColor(error.type);

  return (
    <div className={`${colors.bg} ${colors.border} border rounded-xl p-4 mb-6`}>
      <div className="flex items-start space-x-3">
        <AlertCircle className={`w-5 h-5 ${colors.icon} mt-0.5 flex-shrink-0`} />
        <div className="flex-1">
          <h3 className={`font-medium ${colors.title}`}>
            {error.context || 'Une erreur est survenue'}
          </h3>
          <p className={`${colors.text} text-sm mt-1`}>
            {error.message}
          </p>
          <div className="mt-3 flex space-x-2">
            {error.recoverable && onRetry && (
              <button
                onClick={onRetry}
                className={`${colors.button} text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1`}
              >
                <RefreshCw className="w-3 h-3" />
                <span>Réessayer</span>
              </button>
            )}
            <button
              onClick={onClear}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors"
            >
              Fermer
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Higher-order component that wraps PDF tool pages with:
 * 1. Error boundary for catching React errors
 * 2. Consistent error handling and display
 * 3. Retry functionality
 */
const PDFToolWrapper: React.FC<PDFToolWrapperProps> = ({ children, onRetry }) => {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('PDF Tool Error Boundary:', error, errorInfo);
        // Optional: Send to error tracking service
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

/**
 * Hook that provides enhanced error handling for PDF tools
 * Use this in your PDF tool components for consistent error handling
 */
export const usePDFToolErrorHandler = (onRetry?: () => void) => {
  const { error, handleError, clearError, retryAction } = useErrorHandler();

  const handleFileError = (err: unknown, context: string = 'File Operation') => {
    handleError(err, context, true);
  };

  const handleProcessingError = (err: unknown, toolName: string) => {
    handleError(err, `${toolName} Processing`, true);
  };

  const handleDownloadError = (err: unknown) => {
    handleError(err, 'File Download', true);
  };

  const renderError = () => {
    if (!error) return null;

    return (
      <ErrorDisplay
        error={error}
        onRetry={onRetry ? () => retryAction(onRetry) : undefined}
        onClear={clearError}
      />
    );
  };

  return {
    error,
    hasError: !!error,
    handleFileError,
    handleProcessingError,
    handleDownloadError,
    clearError,
    renderError,
    retryAction,
  };
};

export default PDFToolWrapper;
