import React, { useState } from 'react';
import { Lock, Download, ArrowRight, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const ProtectPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [userPassword, setUserPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [ownerPassword, setOwnerPassword] = useState('');
  const [allowPrinting, setAllowPrinting] = useState(false);
  const [allowCopying, setAllowCopying] = useState(false);
  const [allowModification, setAllowModification] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { downloadAllFiles } = useSession();

  const {
    isProcessing,
    progress,
    message,
    error,
    outputFiles,
    taskId,
    protectPDF,
    resetState
  } = usePDFProcessor();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleProtect = async () => {
    if (files.length === 0 || !userPassword || userPassword !== confirmPassword) return;

    try {
      const parameters = {
        user_password: userPassword,
        owner_password: ownerPassword || undefined,
        allow_printing: allowPrinting,
        allow_copying: allowCopying,
        allow_modification: allowModification,
      };

      await protectPDF(files, parameters, {
        onProgress: (progress, message) => {
          console.log(`Progress: ${progress}% - ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Protection completed:', outputFiles);
        },
        onError: (error) => {
          console.error('Protection failed:', error);
        }
      });
    } catch (err) {
      console.error('Protection operation failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Protéger PDF"
      description="Protégez les fichiers PDF avec un mot de passe. Chiffrez les documents PDF afin d'éviter des accès non autorisés"
      icon={<Lock className="w-8 h-8" />}
      color="from-gray-500 to-gray-600"
      isPremiumOnly={true}
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Protection par mot de passe
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Nouveau mot de passe
                </label>
                <input
                  type="password"
                  placeholder="Entrez un mot de passe sécurisé"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Confirmer le mot de passe
                </label>
                <input
                  type="password"
                  placeholder="Confirmez votre mot de passe"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                />
              </div>

              {password && confirmPassword && password !== confirmPassword && (
                <div className="text-red-600 text-sm">
                  Les mots de passe ne correspondent pas
                </div>
              )}

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="restrict-printing"
                    className="text-gray-600 rounded"
                  />
                  <label htmlFor="restrict-printing" className="text-slate-700">
                    Restreindre l'impression
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="restrict-copying"
                    className="text-gray-600 rounded"
                  />
                  <label htmlFor="restrict-copying" className="text-slate-700">
                    Restreindre la copie de texte
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="restrict-editing"
                    className="text-gray-600 rounded"
                  />
                  <label htmlFor="restrict-editing" className="text-slate-700">
                    Restreindre la modification
                  </label>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-blue-800 font-medium">Conseils de sécurité</span>
                </div>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  <li>• Utilisez un mot de passe fort (8+ caractères)</li>
                  <li>• Combinez lettres, chiffres et symboles</li>
                  <li>• Évitez les informations personnelles</li>
                  <li>• Conservez votre mot de passe en lieu sûr</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && password && password === confirmPassword && (
          <div className="flex justify-center">
            <button
              onClick={handleProtect}
              disabled={isProcessing}
              className="bg-gradient-to-r from-gray-600 to-slate-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Protection en cours...</span>
                </>
              ) : (
                <>
                  <span>Protéger le PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default ProtectPDF;