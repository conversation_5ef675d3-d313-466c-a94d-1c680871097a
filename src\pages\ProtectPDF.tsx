import React, { useState } from 'react';
import { Lock, Download, ArrowRight, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';
import { useAuth } from '../contexts/AuthContext';

const ProtectPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [ownerPassword, setOwnerPassword] = useState('');
  const [allowPrinting, setAllowPrinting] = useState(false);
  const [allowCopying, setAllowCopying] = useState(false);
  const [allowModification, setAllowModification] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { downloadAllFiles } = useSession();
  const { user } = useAuth();

  const {
    isProcessing,
    progress,
    message,
    error,
    outputFiles,
    taskId,
    protectPDF,
    resetState
  } = usePDFProcessor();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleProtect = async () => {
    if (files.length === 0 || !password || password !== confirmPassword) return;

    try {
      const parameters = {
        user_password: password,
        owner_password: ownerPassword || undefined,
        allow_printing: allowPrinting,
        allow_copying: allowCopying,
        allow_modification: allowModification,
      };

      await protectPDF(files, parameters, {
        onProgress: (progress, message) => {
          console.log(`Progress: ${progress}% - ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Protection completed:', outputFiles);
        },
        onError: (error) => {
          console.error('Protection failed:', error);
        }
      });
    } catch (err) {
      console.error('Protection operation failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Protéger PDF"
      description="Protégez les fichiers PDF avec un mot de passe. Chiffrez les documents PDF afin d'éviter des accès non autorisés"
      icon={<Lock className="w-8 h-8" />}
      color="from-gray-500 to-gray-600"
      isPremiumOnly={true}
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Protection par mot de passe
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Nouveau mot de passe
                </label>
                <input
                  type="password"
                  placeholder="Entrez un mot de passe sécurisé"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Confirmer le mot de passe
                </label>
                <input
                  type="password"
                  placeholder="Confirmez votre mot de passe"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500"
                />
              </div>

              {password && confirmPassword && password !== confirmPassword && (
                <div className="text-red-600 text-sm">
                  Les mots de passe ne correspondent pas
                </div>
              )}

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="restrict-printing"
                    checked={!allowPrinting}
                    onChange={(e) => setAllowPrinting(!e.target.checked)}
                    className="text-gray-600 rounded"
                  />
                  <label htmlFor="restrict-printing" className="text-slate-700">
                    Restreindre l'impression
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="restrict-copying"
                    checked={!allowCopying}
                    onChange={(e) => setAllowCopying(!e.target.checked)}
                    className="text-gray-600 rounded"
                  />
                  <label htmlFor="restrict-copying" className="text-slate-700">
                    Restreindre la copie de texte
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="restrict-editing"
                    checked={!allowModification}
                    onChange={(e) => setAllowModification(!e.target.checked)}
                    className="text-gray-600 rounded"
                  />
                  <label htmlFor="restrict-editing" className="text-slate-700">
                    Restreindre la modification
                  </label>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-blue-800 font-medium">Conseils de sécurité</span>
                </div>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  <li>• Utilisez un mot de passe fort (8+ caractères)</li>
                  <li>• Combinez lettres, chiffres et symboles</li>
                  <li>• Évitez les informations personnelles</li>
                  <li>• Conservez votre mot de passe en lieu sûr</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-gray-50 border border-gray-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-600"></div>
              <span className="text-gray-800 font-medium">Protection en cours...</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                className="bg-gray-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la protection</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Protection réussie!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) protégé(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {files.length > 0 && password && password === confirmPassword && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleProtect}
              disabled={isProcessing}
              className="bg-gradient-to-r from-gray-600 to-slate-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Protection en cours...</span>
                </>
              ) : (
                <>
                  <span>Protéger le PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default ProtectPDF;