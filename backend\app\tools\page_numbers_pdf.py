"""
PDF page numbers tool for adding page numbering to PDF documents.
"""
import os
from typing import List, Dict, Any, Optional
from PyPDF2 import PdfWriter, PdfReader
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.colors import Color
from reportlab.lib.units import inch
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFPageNumbersTool(BasePDFTool):
    """Tool for adding page numbers to PDF documents."""
    
    def __init__(self):
        super().__init__("page_numbers")
        
        # Page number positions
        self.positions = {
            "top-left": (0.1, 0.95),
            "top-center": (0.5, 0.95),
            "top-right": (0.9, 0.95),
            "bottom-left": (0.1, 0.05),
            "bottom-center": (0.5, 0.05),
            "bottom-right": (0.9, 0.05),
            "center-left": (0.1, 0.5),
            "center-right": (0.9, 0.5)
        }
        
        # Number formats
        self.formats = {
            "number": lambda n: str(n),
            "roman_lower": lambda n: self._to_roman(n).lower(),
            "roman_upper": lambda n: self._to_roman(n).upper(),
            "letter_lower": lambda n: self._to_letter(n).lower(),
            "letter_upper": lambda n: self._to_letter(n).upper(),
            "custom": lambda n: f"Page {n}"
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Add page numbers to PDF files.
        
        Args:
            input_files: List of PDF file paths to add page numbers to
            output_path: Output directory for numbered PDFs
            parameters: Page numbering parameters (position, format, style, etc.)
            
        Returns:
            List containing paths to numbered PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for page numbering")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        position = params.get("position", "bottom-center")
        format_style = params.get("format_style", "number")
        start_number = params.get("start_number", 1)
        font_size = params.get("font_size", 12)
        color = params.get("color", "black")
        pages = params.get("pages", "all")  # "all", "odd", "even", or specific pages
        prefix = params.get("prefix", "")
        suffix = params.get("suffix", "")
        
        # Validate parameters
        if position not in self.positions:
            raise ValidationError(f"Invalid position: {position}. Available: {list(self.positions.keys())}")
        
        if format_style not in self.formats:
            raise ValidationError(f"Invalid format: {format_style}. Available: {list(self.formats.keys())}")
        
        if not isinstance(start_number, int) or start_number < 1:
            raise ValidationError("Start number must be a positive integer")
        
        if not isinstance(font_size, int) or font_size < 6 or font_size > 72:
            raise ValidationError("Font size must be between 6 and 72")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting page numbering",
                input_count=len(input_files),
                position=position,
                format_style=format_style,
                start_number=start_number,
                font_size=font_size
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Adding page numbers to file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}_numbered.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Add page numbers to the PDF
                    numbering_result = await self._add_page_numbers(
                        input_file,
                        output_file,
                        position,
                        format_style,
                        start_number,
                        font_size,
                        color,
                        pages,
                        prefix,
                        suffix
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create numbered PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} numbered successfully",
                        input_file=input_file,
                        output_file=output_file,
                        pages_numbered=numbering_result["pages_numbered"],
                        total_pages=numbering_result["total_pages"],
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to add page numbers to file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to add page numbers to {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "Page numbering completed successfully",
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during page numbering: {str(e)}")
    
    async def _add_page_numbers(
        self,
        input_file: str,
        output_file: str,
        position: str,
        format_style: str,
        start_number: int,
        font_size: int,
        color: str,
        pages: str,
        prefix: str,
        suffix: str
    ) -> Dict[str, Any]:
        """Add page numbers to a PDF file."""
        try:
            # Read the input PDF
            with open(input_file, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                # Check if PDF is encrypted
                if pdf_reader.is_encrypted:
                    raise ProcessingError("Cannot add page numbers to encrypted PDF")
                
                total_pages = len(pdf_reader.pages)
                
                # Determine which pages to number
                pages_to_number = self._parse_pages_parameter(pages, total_pages)
                
                # Create PDF writer
                pdf_writer = PdfWriter()
                
                pages_numbered = 0
                current_number = start_number
                
                # Process each page
                for page_num in range(total_pages):
                    page = pdf_reader.pages[page_num]
                    
                    # Add page number if this page should be numbered
                    if (page_num + 1) in pages_to_number:
                        # Get page dimensions
                        page_width = float(page.mediabox.width)
                        page_height = float(page.mediabox.height)
                        
                        # Create page number overlay
                        number_text = self._format_page_number(
                            current_number, format_style, prefix, suffix
                        )
                        
                        overlay_pdf = self._create_page_number_overlay(
                            number_text,
                            position,
                            font_size,
                            color,
                            page_width,
                            page_height
                        )
                        
                        # Read the overlay PDF
                        overlay_reader = PdfReader(overlay_pdf)
                        overlay_page = overlay_reader.pages[0]
                        
                        # Merge the overlay with the page
                        page.merge_page(overlay_page)
                        
                        # Clean up temporary overlay file
                        os.remove(overlay_pdf)
                        
                        pages_numbered += 1
                        current_number += 1
                        
                        self.logger.debug(f"Added page number to page {page_num + 1}: {number_text}")
                    
                    # Add the page to the writer
                    pdf_writer.add_page(page)
                
                # Copy metadata if available
                if pdf_reader.metadata:
                    pdf_writer.add_metadata(pdf_reader.metadata)
                
                # Write the numbered PDF
                with open(output_file, 'wb') as output_pdf:
                    pdf_writer.write(output_pdf)
                
                return {
                    "pages_numbered": pages_numbered,
                    "total_pages": total_pages,
                    "start_number": start_number,
                    "format_style": format_style
                }
                
        except Exception as e:
            raise ProcessingError(f"Failed to add page numbers: {str(e)}")
    
    def _create_page_number_overlay(
        self,
        number_text: str,
        position: str,
        font_size: int,
        color: str,
        page_width: float,
        page_height: float
    ) -> str:
        """Create a temporary PDF with the page number overlay."""
        temp_overlay_file = f"temp_page_number_{os.getpid()}.pdf"
        
        try:
            # Create canvas with the same size as the page
            c = canvas.Canvas(temp_overlay_file, pagesize=(page_width, page_height))
            
            # Set font and color
            c.setFont("Helvetica", font_size)
            
            # Set color
            page_color = self._get_color(color)
            c.setFillColor(page_color)
            
            # Calculate position
            pos_x, pos_y = self.positions[position]
            x = page_width * pos_x
            y = page_height * pos_y
            
            # Adjust position based on text alignment
            if pos_x == 0.5:  # Center alignment
                text_width = c.stringWidth(number_text, "Helvetica", font_size)
                x -= text_width / 2
            elif pos_x == 0.9:  # Right alignment
                text_width = c.stringWidth(number_text, "Helvetica", font_size)
                x -= text_width
            
            # Draw the page number
            c.drawString(x, y, number_text)
            
            # Save the PDF
            c.save()
            
            return temp_overlay_file
            
        except Exception as e:
            # Clean up if there was an error
            if os.path.exists(temp_overlay_file):
                os.remove(temp_overlay_file)
            raise ProcessingError(f"Failed to create page number overlay: {str(e)}")
    
    def _format_page_number(self, number: int, format_style: str, prefix: str, suffix: str) -> str:
        """Format page number according to the specified style."""
        try:
            formatted_number = self.formats[format_style](number)
            return f"{prefix}{formatted_number}{suffix}"
        except Exception as e:
            self.logger.warning(f"Failed to format page number: {str(e)}")
            return f"{prefix}{number}{suffix}"
    
    def _to_roman(self, number: int) -> str:
        """Convert number to Roman numerals."""
        values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1]
        symbols = ["M", "CM", "D", "CD", "C", "XC", "L", "XL", "X", "IX", "V", "IV", "I"]
        
        result = ""
        for i in range(len(values)):
            count = number // values[i]
            result += symbols[i] * count
            number -= values[i] * count
        
        return result
    
    def _to_letter(self, number: int) -> str:
        """Convert number to letter sequence (A, B, C, ..., Z, AA, BB, ...)."""
        if number <= 26:
            return chr(64 + number)  # A=65, B=66, etc.
        else:
            # For numbers > 26, use AA, BB, CC pattern
            base_letter = chr(64 + ((number - 1) % 26) + 1)
            repeat_count = (number - 1) // 26 + 1
            return base_letter * repeat_count

    def _get_color(self, color_name: str) -> Color:
        """Convert color name to ReportLab Color object."""
        color_map = {
            "black": Color(0, 0, 0),
            "white": Color(1, 1, 1),
            "red": Color(1, 0, 0),
            "green": Color(0, 1, 0),
            "blue": Color(0, 0, 1),
            "gray": Color(0.5, 0.5, 0.5),
            "grey": Color(0.5, 0.5, 0.5),
            "lightgray": Color(0.8, 0.8, 0.8),
            "darkgray": Color(0.3, 0.3, 0.3)
        }

        return color_map.get(color_name.lower(), Color(0, 0, 0))

    def _parse_pages_parameter(self, pages: str, total_pages: int) -> set:
        """Parse the pages parameter to get set of page numbers to number."""
        if pages == "all":
            return set(range(1, total_pages + 1))
        elif pages == "odd":
            return set(range(1, total_pages + 1, 2))
        elif pages == "even":
            return set(range(2, total_pages + 1, 2))
        else:
            # Parse specific pages (e.g., "1,3,5-7,10")
            page_numbers = set()

            for part in pages.split(','):
                part = part.strip()

                if '-' in part:
                    # Range like "5-7"
                    start_str, end_str = part.split('-', 1)
                    start = int(start_str.strip())
                    end = int(end_str.strip())

                    if start < 1 or end > total_pages or start > end:
                        raise ValidationError(f"Invalid page range: {part}")

                    page_numbers.update(range(start, end + 1))
                else:
                    # Single page like "1"
                    page = int(part)

                    if page < 1 or page > total_pages:
                        raise ValidationError(f"Invalid page number: {page}")

                    page_numbers.add(page)

            return page_numbers

    def get_numbering_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available page numbering options."""
        return {
            "position": {
                "description": "Position of page numbers on the page",
                "type": "string",
                "options": list(self.positions.keys()),
                "default": "bottom-center"
            },
            "format_style": {
                "description": "Format style for page numbers",
                "type": "string",
                "options": list(self.formats.keys()),
                "default": "number",
                "examples": {
                    "number": "1, 2, 3, ...",
                    "roman_lower": "i, ii, iii, ...",
                    "roman_upper": "I, II, III, ...",
                    "letter_lower": "a, b, c, ...",
                    "letter_upper": "A, B, C, ...",
                    "custom": "Page 1, Page 2, ..."
                }
            },
            "start_number": {
                "description": "Starting number for page numbering",
                "type": "integer",
                "min": 1,
                "max": 9999,
                "default": 1
            },
            "font_size": {
                "description": "Font size for page numbers",
                "type": "integer",
                "min": 6,
                "max": 72,
                "default": 12
            },
            "color": {
                "description": "Color of page numbers",
                "type": "string",
                "options": ["black", "white", "red", "green", "blue", "gray", "lightgray", "darkgray"],
                "default": "black"
            },
            "pages": {
                "description": "Pages to add numbers to",
                "type": "string",
                "options": ["all", "odd", "even", "specific (e.g., 1,3,5-7)"],
                "default": "all"
            },
            "prefix": {
                "description": "Text to add before the page number",
                "type": "string",
                "default": "",
                "example": "Page "
            },
            "suffix": {
                "description": "Text to add after the page number",
                "type": "string",
                "default": "",
                "example": " of X"
            }
        }

    def get_available_positions(self) -> List[str]:
        """Get list of available page number positions."""
        return list(self.positions.keys())

    def get_available_formats(self) -> List[str]:
        """Get list of available page number formats."""
        return list(self.formats.keys())

    async def preview_page_numbering(
        self,
        total_pages: int,
        format_style: str,
        start_number: int,
        pages: str,
        prefix: str = "",
        suffix: str = ""
    ) -> Dict[str, Any]:
        """Preview page numbering without actually applying it."""
        try:
            pages_to_number = self._parse_pages_parameter(pages, total_pages)

            # Generate preview of first few page numbers
            preview_numbers = []
            current_number = start_number

            for page_num in range(1, min(total_pages + 1, 11)):  # First 10 pages
                if page_num in pages_to_number:
                    formatted_number = self._format_page_number(
                        current_number, format_style, prefix, suffix
                    )
                    preview_numbers.append({
                        "page": page_num,
                        "number": formatted_number
                    })
                    current_number += 1
                else:
                    preview_numbers.append({
                        "page": page_num,
                        "number": "(no number)"
                    })

            return {
                "total_pages": total_pages,
                "pages_to_number": len(pages_to_number),
                "format_style": format_style,
                "start_number": start_number,
                "prefix": prefix,
                "suffix": suffix,
                "preview_numbers": preview_numbers,
                "preview_truncated": total_pages > 10,
                "is_valid": True
            }

        except Exception as e:
            return {
                "total_pages": total_pages,
                "format_style": format_style,
                "error": str(e),
                "is_valid": False
            }

    def format_number_examples(self, number: int = 5) -> Dict[str, str]:
        """Get examples of different number formats."""
        examples = {}

        for format_name, format_func in self.formats.items():
            try:
                examples[format_name] = format_func(number)
            except Exception as e:
                examples[format_name] = f"Error: {str(e)}"

        return examples


# Create tool instance
page_numbers_pdf_tool = PDFPageNumbersTool()
