import React, { useState } from 'react';
import { Minimize2, Download, ArrowR<PERSON>, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const CompressPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [compressionLevel, setCompressionLevel] = useState<'low' | 'medium' | 'high'>('medium');
  const [imageQuality, setImageQuality] = useState(85);
  const [removeMetadata, setRemoveMetadata] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { t } = useLanguage();
  const { downloadAllFiles } = useSession();

  const {
    isProcessing,
    progress,
    message,
    error,
    outputFiles,
    taskId,
    compressPDF,
    resetState
  } = usePDFProcessor();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleCompress = async () => {
    if (files.length === 0) return;

    try {
      const parameters = {
        compression_level: compressionLevel,
        image_quality: imageQuality,
        remove_metadata: removeMetadata,
      };

      await compressPDF(files, parameters, {
        onProgress: (progress, message) => {
          console.log(`Progress: ${progress}% - ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Compression completed:', outputFiles);
        },
        onError: (error) => {
          console.error('Compression failed:', error);
        }
      });
    } catch (err) {
      console.error('Compression operation failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title={t('tool.compress.title')}
      description={t('tool.compress.description')}
      icon={<Minimize2 className="w-8 h-8" />}
      color="from-green-500 to-green-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-700">
                Niveau de compression
              </h3>
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center space-x-2 text-green-600 hover:text-green-700 transition-colors"
              >
                <Settings className="w-4 h-4" />
                <span className="text-sm">Options avancées</span>
              </button>
            </div>

            <div className="space-y-4">
              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="compression"
                    value="low"
                    checked={compressionLevel === 'low'}
                    onChange={(e) => setCompressionLevel(e.target.value as 'low')}
                    className="text-green-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">Compression légère</span>
                    <p className="text-sm text-slate-500">Qualité maximale, taille légèrement réduite</p>
                  </div>
                </div>
                <div className="text-green-600 font-medium">~10-20%</div>
              </label>

              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="compression"
                    value="medium"
                    checked={compressionLevel === 'medium'}
                    onChange={(e) => setCompressionLevel(e.target.value as 'medium')}
                    className="text-green-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">Compression recommandée</span>
                    <p className="text-sm text-slate-500">Bon équilibre entre qualité et taille</p>
                  </div>
                </div>
                <div className="text-green-600 font-medium">~30-50%</div>
              </label>

              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="compression"
                    value="high"
                    checked={compressionLevel === 'high'}
                    onChange={(e) => setCompressionLevel(e.target.value as 'high')}
                    className="text-green-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">Compression maximale</span>
                    <p className="text-sm text-slate-500">Taille minimale, qualité réduite</p>
                  </div>
                </div>
                <div className="text-green-600 font-medium">~60-80%</div>
              </label>
            </div>

            {showAdvanced && (
              <div className="mt-6 p-4 bg-white rounded-lg border">
                <h4 className="text-md font-medium text-slate-700 mb-3">Options avancées</h4>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Qualité des images: {imageQuality}%
                    </label>
                    <input
                      type="range"
                      min="10"
                      max="100"
                      value={imageQuality}
                      onChange={(e) => setImageQuality(parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-slate-500 mt-1">
                      <span>Petite taille</span>
                      <span>Haute qualité</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="removeMetadata"
                      checked={removeMetadata}
                      onChange={(e) => setRemoveMetadata(e.target.checked)}
                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                    />
                    <label htmlFor="removeMetadata" className="text-sm text-slate-700">
                      Supprimer les métadonnées pour réduire la taille
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-green-600"></div>
              <span className="text-green-800 font-medium">Compression en cours...</span>
            </div>
            <div className="w-full bg-green-200 rounded-full h-2 mb-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-green-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la compression</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Compression réussie!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) compressé(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {/* Compress Button */}
        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleCompress}
              disabled={isProcessing}
              className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Compression en cours...</span>
                </>
              ) : (
                <>
                  <Minimize2 className="w-5 h-5" />
                  <span>{t('button.compress')}</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default CompressPDF;