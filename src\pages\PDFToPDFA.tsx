import { useState } from 'react';
import { FileText, Download, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const PDFToPDFA = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    convertPDFToPDFA, resetState
  } = usePDFProcessor();
  
  const [files, setFiles] = useState<File[]>([]);
  const [conformanceLevel, setConformanceLevel] = useState<'1a' | '1b' | '2a' | '2b' | '3a' | '3b'>('2b');
  const [colorProfile, setColorProfile] = useState<'sRGB' | 'Adobe_RGB' | 'ProPhoto_RGB'>('sRGB');
  const [embedFonts, setEmbedFonts] = useState(true);
  const [optimizeImages, setOptimizeImages] = useState(true);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleConvert = async () => {
    if (files.length === 0) return;
    
    try {
      await convertPDFToPDFA(files, {
        conformance_level: conformanceLevel,
        color_profile: colorProfile,
        embed_fonts: embedFonts,
        optimize_images: optimizeImages
      });
    } catch (err) {
      console.error('PDF to PDF/A conversion failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="PDF en PDF/A"
      description="Transformez votre PDF en PDF/A, la version au standard ISO des PDF, pour un archivage à long-terme"
      icon={<FileText className="w-8 h-8" />}
      color="from-zinc-500 to-zinc-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Version PDF/A
            </h3>
            
            <div className="space-y-4">
              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="pdfAVersion"
                    value="1"
                    checked={pdfAVersion === '1'}
                    onChange={(e) => setPdfAVersion(e.target.value as '1')}
                    className="text-zinc-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">PDF/A-1</span>
                    <p className="text-sm text-slate-500">Basé sur PDF 1.4, compatible avec la plupart des systèmes</p>
                  </div>
                </div>
                <div className="text-zinc-600 font-medium">ISO 19005-1</div>
              </label>

              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="pdfAVersion"
                    value="2"
                    checked={pdfAVersion === '2'}
                    onChange={(e) => setPdfAVersion(e.target.value as '2')}
                    className="text-zinc-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">PDF/A-2 (recommandé)</span>
                    <p className="text-sm text-slate-500">Basé sur PDF 1.7, support des signatures et formulaires</p>
                  </div>
                </div>
                <div className="text-zinc-600 font-medium">ISO 19005-2</div>
              </label>

              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="pdfAVersion"
                    value="3"
                    checked={pdfAVersion === '3'}
                    onChange={(e) => setPdfAVersion(e.target.value as '3')}
                    className="text-zinc-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">PDF/A-3</span>
                    <p className="text-sm text-slate-500">Permet l'intégration de fichiers dans n'importe quel format</p>
                  </div>
                </div>
                <div className="text-zinc-600 font-medium">ISO 19005-3</div>
              </label>
            </div>

            <div className="mt-6 bg-green-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-green-800 font-medium">Avantages PDF/A</span>
              </div>
              <ul className="text-sm text-green-700 mt-1 space-y-1">
                <li>• Archivage à long terme garanti</li>
                <li>• Conformité aux standards ISO</li>
                <li>• Indépendance des logiciels et matériels</li>
                <li>• Préservation de l'apparence visuelle</li>
              </ul>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-zinc-50 border border-zinc-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-zinc-600"></div>
              <span className="text-zinc-800 font-medium">Conversion en cours...</span>
            </div>
            <div className="w-full bg-zinc-200 rounded-full h-2 mb-2">
              <div
                className="bg-zinc-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-zinc-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la conversion</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Conversion réussie!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) PDF/A généré(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {/* Convert Button */}
        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-zinc-600 to-slate-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <FileText className="w-5 h-5" />
                  <span>Convertir en PDF/A</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default PDFToPDFA;