<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        #log { background: #f5f5f5; padding: 10px; height: 300px; overflow-y: scroll; }
    </style>
</head>
<body>
    <h1>Frontend API Test</h1>
    
    <div class="test-section">
        <h2>API Connection Test</h2>
        <button onclick="testHealthCheck()">Test Health Check</button>
        <button onclick="testCreateSession()">Test Create Session</button>
        <button onclick="testMergeAPI()">Test Merge API Structure</button>
    </div>
    
    <div class="test-section">
        <h2>File Upload Test</h2>
        <input type="file" id="fileInput" multiple accept=".pdf">
        <button onclick="testMergeWithFiles()">Test Merge with Files</button>
    </div>
    
    <div class="test-section">
        <h2>Log Output</h2>
        <div id="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function testHealthCheck() {
            log('Testing health check...');
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                log(`Health check successful: ${JSON.stringify(data)}`, 'success');
            } catch (error) {
                log(`Health check failed: ${error.message}`, 'error');
            }
        }
        
        async function testCreateSession() {
            log('Testing session creation...');
            try {
                const response = await fetch(`${API_BASE_URL}/api/tools/session`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`Session created successfully: ${data.session_id}`, 'success');
                window.testSessionId = data.session_id;
            } catch (error) {
                log(`Session creation failed: ${error.message}`, 'error');
            }
        }
        
        function testMergeAPI() {
            log('Testing merge API structure...');
            
            // Test if we can create a basic API client structure
            class TestAPIClient {
                constructor() {
                    this.baseURL = API_BASE_URL;
                }
                
                createFormData(files, sessionId, parameters = {}) {
                    log('createFormData method called');
                    const formData = new FormData();
                    formData.append('session_id', sessionId);
                    files.forEach(file => {
                        formData.append('files', file);
                    });
                    Object.entries(parameters).forEach(([key, value]) => {
                        if (value !== undefined && value !== null) {
                            formData.append(key, String(value));
                        }
                    });
                    return formData;
                }
                
                async mergePDF(files, sessionId, parameters = {}) {
                    log(`mergePDF called with ${files.length} files`);
                    const formData = this.createFormData(files, sessionId, parameters);
                    
                    const response = await fetch(`${this.baseURL}/api/tools/merge`, {
                        method: 'POST',
                        body: formData,
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    return response.json();
                }
            }
            
            const testClient = new TestAPIClient();
            log(`API Client created successfully. Has createFormData: ${typeof testClient.createFormData === 'function'}`, 'success');
            log(`API Client has mergePDF: ${typeof testClient.mergePDF === 'function'}`, 'success');
            
            window.testAPIClient = testClient;
        }
        
        async function testMergeWithFiles() {
            const fileInput = document.getElementById('fileInput');
            const files = Array.from(fileInput.files);
            
            if (files.length < 2) {
                log('Please select at least 2 PDF files', 'error');
                return;
            }
            
            if (!window.testSessionId) {
                log('Please create a session first', 'error');
                return;
            }
            
            if (!window.testAPIClient) {
                log('Please test API structure first', 'error');
                return;
            }
            
            log(`Testing merge with ${files.length} files...`);
            
            try {
                const result = await window.testAPIClient.mergePDF(
                    files, 
                    window.testSessionId, 
                    { add_bookmarks: true }
                );
                
                log(`Merge request successful: ${JSON.stringify(result)}`, 'success');
            } catch (error) {
                log(`Merge request failed: ${error.message}`, 'error');
            }
        }
        
        // Initialize
        log('Frontend API Test initialized');
        log(`API Base URL: ${API_BASE_URL}`);
    </script>
</body>
</html>
