import { useState } from 'react';
import { Hash, Download, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const PageNumbers = () => {
  const { downloadAllFiles } = useSession();
  const { addPageNumbers, isProcessing, progress, error, outputFiles, taskId, resetState } = usePDFProcessor();
  
  const [files, setFiles] = useState<File[]>([]);
  const [position, setPosition] = useState<'bottom-center' | 'bottom-left' | 'bottom-right' | 'top-center' | 'top-left' | 'top-right'>('bottom-center');
  const [format, setFormat] = useState<'number' | 'x-of-y' | 'roman'>('number');
  const [skipFirstPage, setSkipFirstPage] = useState(false);
  const [startNumber, setStartNumber] = useState(1);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleAddPageNumbers = async () => {
    if (files.length === 0) return;

    const options = {
      position,
      format,
      skip_first_page: skipFirstPage,
      start_number: startNumber
    };

    try {
      await addPageNumbers(files, options);
    } catch (err) {
      console.error('Error adding page numbers:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Numéros de pages"
      description="Insérez des numéros de pages dans les documents PDF, en toute simplicité"
      icon={<Hash className="w-8 h-8" />}
      color="from-fuchsia-500 to-fuchsia-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Configuration des numéros de pages
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Position
                </label>
                <div className="grid grid-cols-3 gap-2">
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="top-left"
                      checked={position === 'top-left'}
                      onChange={(e) => setPosition(e.target.value as 'top-left')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Haut gauche</span>
                  </label>
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="top-center"
                      checked={position === 'top-center'}
                      onChange={(e) => setPosition(e.target.value as 'top-center')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Haut centre</span>
                  </label>
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="top-right"
                      checked={position === 'top-right'}
                      onChange={(e) => setPosition(e.target.value as 'top-right')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Haut droite</span>
                  </label>
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="bottom-left"
                      checked={position === 'bottom-left'}
                      onChange={(e) => setPosition(e.target.value as 'bottom-left')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Bas gauche</span>
                  </label>
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="bottom-center"
                      checked={position === 'bottom-center'}
                      onChange={(e) => setPosition(e.target.value as 'bottom-center')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Bas centre</span>
                  </label>
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="bottom-right"
                      checked={position === 'bottom-right'}
                      onChange={(e) => setPosition(e.target.value as 'bottom-right')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Bas droite</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Format
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="format"
                      value="number"
                      checked={format === 'number'}
                      onChange={(e) => setFormat(e.target.value as 'number')}
                      className="text-fuchsia-600"
                    />
                    <span className="text-slate-700">Numérique (1, 2, 3...)</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="format"
                      value="x-of-y"
                      checked={format === 'x-of-y'}
                      onChange={(e) => setFormat(e.target.value as 'x-of-y')}
                      className="text-fuchsia-600"
                    />
                    <span className="text-slate-700">X sur Y (1 sur 10, 2 sur 10...)</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="format"
                      value="roman"
                      checked={format === 'roman'}
                      onChange={(e) => setFormat(e.target.value as 'roman')}
                      className="text-fuchsia-600"
                    />
                    <span className="text-slate-700">Chiffres romains (I, II, III...)</span>
                  </label>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="skip-first-page"
                    checked={skipFirstPage}
                    onChange={(e) => setSkipFirstPage(e.target.checked)}
                    className="text-fuchsia-600 rounded"
                  />
                  <label htmlFor="skip-first-page" className="text-slate-700">
                    Ignorer la première page
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <label htmlFor="start-number" className="text-slate-700 min-w-0 flex-shrink-0">
                    Commencer à partir du numéro:
                  </label>
                  <input
                    type="number"
                    id="start-number"
                    value={startNumber}
                    onChange={(e) => setStartNumber(parseInt(e.target.value) || 1)}
                    min="1"
                    className="w-20 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-fuchsia-500"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <span className="text-blue-800">Ajout des numéros de pages en cours...</span>
            </div>
            {progress > 0 && (
              <div className="mt-3">
                <div className="bg-blue-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-blue-600 mt-1">{progress}% terminé</p>
              </div>
            )}
          </div>
        )}

        {/* Error Status */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-red-800">Erreur lors du traitement</h3>
                <p className="text-red-700 text-sm mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success Status */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-medium text-green-800">Numéros de pages ajoutés avec succès!</h3>
                <p className="text-green-700 text-sm mt-1">
                  Votre fichier avec les numéros de pages est prêt à être téléchargé.
                </p>
                <div className="mt-3">
                  <button
                    onClick={handleDownload}
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Télécharger
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleAddPageNumbers}
              disabled={isProcessing}
              className="bg-gradient-to-r from-fuchsia-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Ajout des numéros...</span>
                </>
              ) : (
                <>
                  <span>Ajouter les numéros de pages</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default PageNumbers;