"""
Base class for all PDF processing tools.
"""
import os
import time
import async<PERSON>
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import structlog

logger = structlog.get_logger()


class BasePDFTool(ABC):
    """Base class for all PDF processing tools."""
    
    def __init__(self, tool_name: str):
        self.tool_name = tool_name
        self.logger = logger.bind(tool=tool_name)
    
    @abstractmethod
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Process the input files and return list of output file paths.
        
        Args:
            input_files: List of input file paths
            output_path: Directory or file path for output
            parameters: Tool-specific parameters
            
        Returns:
            List of output file paths
        """
        pass
    
    def validate_input_files(self, input_files: List[str]) -> bool:
        """Validate input files exist and are accessible."""
        for file_path in input_files:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Input file not found: {file_path}")
            
            if not os.access(file_path, os.R_OK):
                raise PermissionError(f"Cannot read input file: {file_path}")
        
        return True
    
    def validate_pdf_files(self, input_files: List[str]) -> bool:
        """Validate that input files are PDF files."""
        for file_path in input_files:
            if not file_path.lower().endswith('.pdf'):
                raise ValueError(f"File is not a PDF: {file_path}")
        
        return True
    
    def ensure_output_directory(self, output_path: str) -> str:
        """Ensure output directory exists and return the path."""
        if os.path.isfile(output_path):
            output_dir = os.path.dirname(output_path)
        else:
            output_dir = output_path
        
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def get_file_size_mb(self, file_path: str) -> float:
        """Get file size in MB."""
        if os.path.exists(file_path):
            return os.path.getsize(file_path) / (1024 * 1024)
        return 0.0
    
    async def process_with_timeout(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None,
        timeout_seconds: int = 300
    ) -> List[str]:
        """
        Process files with a timeout.
        
        Args:
            input_files: List of input file paths
            output_path: Directory or file path for output
            parameters: Tool-specific parameters
            timeout_seconds: Maximum processing time in seconds
            
        Returns:
            List of output file paths
        """
        start_time = time.time()
        
        try:
            # Validate inputs
            self.validate_input_files(input_files)
            
            # Log processing start
            total_size = sum(self.get_file_size_mb(f) for f in input_files)
            self.logger.info(
                "Starting processing",
                input_files=len(input_files),
                total_size_mb=round(total_size, 2),
                parameters=parameters
            )
            
            # Process with timeout
            result = await asyncio.wait_for(
                self.process(input_files, output_path, parameters),
                timeout=timeout_seconds
            )
            
            # Log success
            processing_time = time.time() - start_time
            output_size = sum(self.get_file_size_mb(f) for f in result if os.path.exists(f))
            
            self.logger.info(
                "Processing completed successfully",
                output_files=len(result),
                processing_time=round(processing_time, 2),
                output_size_mb=round(output_size, 2)
            )
            
            return result
            
        except asyncio.TimeoutError:
            processing_time = time.time() - start_time
            self.logger.error(
                "Processing timed out",
                timeout_seconds=timeout_seconds,
                processing_time=round(processing_time, 2)
            )
            raise TimeoutError(f"Processing timed out after {timeout_seconds} seconds")
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(
                "Processing failed",
                error=str(e),
                processing_time=round(processing_time, 2)
            )
            raise e
    
    def cleanup_temp_files(self, file_paths: List[str]):
        """Clean up temporary files."""
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    self.logger.debug("Cleaned up temp file", file_path=file_path)
            except Exception as e:
                self.logger.warning("Failed to cleanup temp file", file_path=file_path, error=str(e))
    
    def get_output_filename(self, input_filename: str, suffix: str = "") -> str:
        """Generate output filename based on input filename."""
        base_name = os.path.splitext(os.path.basename(input_filename))[0]
        if suffix:
            return f"{base_name}_{suffix}.pdf"
        else:
            return f"{base_name}_{self.tool_name}.pdf"
    
    def log_file_info(self, file_path: str, label: str = "file"):
        """Log information about a file."""
        if os.path.exists(file_path):
            size_mb = self.get_file_size_mb(file_path)
            self.logger.debug(
                f"{label} info",
                path=file_path,
                size_mb=round(size_mb, 2)
            )
        else:
            self.logger.warning(f"{label} not found", path=file_path)


class ToolError(Exception):
    """Base exception for tool errors."""
    pass


class ValidationError(ToolError):
    """Exception for validation errors."""
    pass


class ProcessingError(ToolError):
    """Exception for processing errors."""
    pass
