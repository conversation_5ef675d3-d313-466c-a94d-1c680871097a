/**
 * Integration test script to verify frontend-backend connectivity
 * Run with: node test-integration.js
 */

const API_BASE_URL = 'http://localhost:8000';

async function testAPIConnection() {
  console.log('🧪 Testing PDF Processing Platform Integration\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing health endpoint...');
    const healthResponse = await fetch(`${API_BASE_URL}/health`);
    if (healthResponse.ok) {
      const health = await healthResponse.json();
      console.log('✅ Health check passed:', health.status);
    } else {
      console.log('❌ Health check failed');
      return;
    }

    // Test 2: Create Session
    console.log('\n2. Testing session creation...');
    const sessionResponse = await fetch(`${API_BASE_URL}/api/tools/session`, {
      method: 'POST',
    });
    
    if (sessionResponse.ok) {
      const session = await sessionResponse.json();
      console.log('✅ Session created:', session.session_id);
      
      // Test 3: Get Session Status
      console.log('\n3. Testing session status...');
      const statusResponse = await fetch(`${API_BASE_URL}/api/tools/session/${session.session_id}`);
      if (statusResponse.ok) {
        const status = await statusResponse.json();
        console.log('✅ Session status retrieved');
      } else {
        console.log('❌ Session status failed');
      }
    } else {
      console.log('❌ Session creation failed');
      return;
    }

    // Test 4: Check Tool Endpoints
    console.log('\n4. Testing tool information endpoints...');
    const toolEndpoints = [
      '/api/tools/compress/levels',
      '/api/tools/split/options',
      '/api/tools/ocr/languages',
      '/api/tools/repair/strategies',
      '/api/tools/organize/operations',
      '/api/tools/crop/modes',
      '/api/tools/page-numbers/formats',
      '/api/tools/redact/patterns',
      '/api/tools/compare/options',
      '/api/tools/sign/options'
    ];

    let successCount = 0;
    for (const endpoint of toolEndpoints) {
      try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`);
        if (response.ok) {
          successCount++;
          console.log(`✅ ${endpoint}`);
        } else {
          console.log(`❌ ${endpoint} - Status: ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ ${endpoint} - Error: ${error.message}`);
      }
    }

    console.log(`\n📊 Tool endpoints: ${successCount}/${toolEndpoints.length} working`);

    // Test 5: API Documentation
    console.log('\n5. Testing API documentation...');
    const docsResponse = await fetch(`${API_BASE_URL}/docs`);
    if (docsResponse.ok) {
      console.log('✅ API documentation accessible at /docs');
    } else {
      console.log('❌ API documentation not accessible');
    }

    console.log('\n🎉 Integration test completed!');
    console.log('\n📋 Summary:');
    console.log('- Backend API is running and accessible');
    console.log('- Session management is working');
    console.log('- Tool endpoints are responding');
    console.log('- Ready for frontend integration');

  } catch (error) {
    console.log('❌ Connection failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the backend is running: cd backend && python run.py');
    console.log('2. Check if the API is accessible at http://localhost:8000');
    console.log('3. Verify CORS settings in backend configuration');
  }
}

// Run the test
testAPIConnection();
