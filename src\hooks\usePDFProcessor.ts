/**
 * Custom hook for PDF processing operations
 * Provides a unified interface for all PDF tools
 */

import { useState, useCallback } from 'react';
import { useSession } from '../contexts/SessionContext';
import { apiClient, ProcessingResponse } from '../services/api';

// Enhanced API client validation with retry mechanism
const waitForApiClient = async (maxRetries = 5, delay = 200): Promise<void> => {
  for (let i = 0; i < maxRetries; i++) {
    if (apiClient &&
        typeof apiClient === 'object' &&
        typeof apiClient.createSession === 'function' &&
        typeof apiClient.splitPDF === 'function') {
      return;
    }
    await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
  }
  throw new Error('API Client failed to initialize after multiple attempts. Please refresh the page.');
};

export interface ProcessingState {
  isProcessing: boolean;
  progress: number;
  message: string;
  error: string | null;
  taskId: string | null;
  outputFiles: string[] | null;
}

export interface ProcessingOptions {
  onProgress?: (progress: number, message: string) => void;
  onComplete?: (outputFiles: string[]) => void;
  onError?: (error: string) => void;
}

export const usePDFProcessor = () => {
  const { ensureSession, addTask, pollTaskStatus, createSession } = useSession();
  
  const [state, setState] = useState<ProcessingState>({
    isProcessing: false,
    progress: 0,
    message: '',
    error: null,
    taskId: null,
    outputFiles: null,
  });

  const resetState = useCallback(() => {
    setState({
      isProcessing: false,
      progress: 0,
      message: '',
      error: null,
      taskId: null,
      outputFiles: null,
    });
  }, []);

  const processFiles = useCallback(async (
    files: File[],
    toolName: string,
    apiMethod: (files: File[], sessionId: string, parameters?: any) => Promise<ProcessingResponse>,
    parameters: any = {},
    options: ProcessingOptions = {}
  ): Promise<string[]> => {
    resetState();
    
    try {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        message: 'Initializing...',
      }));

      // Wait for API client to be ready
      await waitForApiClient();

      setState(prev => ({
        ...prev,
        message: 'Creating session...',
        progress: 5,
      }));

      // Ensure we have a valid session
      const session = await ensureSession();
      let sessionId = session.sessionId;

      // Validate session exists on backend
      setState(prev => ({
        ...prev,
        message: 'Validating session...',
        progress: 8,
      }));

      try {
        await apiClient.getSessionStatus(sessionId);
        console.log('Session validation successful:', sessionId);
      } catch (sessionError) {
        console.warn('Session validation failed, creating new session:', sessionError);
        // Clear the invalid session and create a new one
        try {
          const newSession = await createSession();
          sessionId = newSession.sessionId;
          console.log('New session created:', sessionId);
        } catch (createError) {
          console.error('Failed to create new session:', createError);
          throw new Error('Unable to establish session. Please refresh the page and try again.');
        }
      }

      setState(prev => ({
        ...prev,
        message: 'Starting processing...',
        progress: 10,
      }));

      // Start processing with validated session
      const response = await apiMethod(files, sessionId, parameters);
      
      setState(prev => ({
        ...prev,
        taskId: response.task_id,
        message: response.message,
        progress: 20,
      }));

      // Add task to session
      addTask(response.task_id, toolName);

      // Poll for completion
      const completedTask = await pollTaskStatus(response.task_id, (task) => {
        setState(prev => ({
          ...prev,
          progress: task.progress || prev.progress,
          message: task.message || prev.message,
        }));
        
        if (options.onProgress) {
          options.onProgress(task.progress || 0, task.message || '');
        }
      });

      if (completedTask.status === 'completed') {
        // Task completed successfully
        const outputFiles = completedTask.outputFiles || [];

        setState(prev => ({
          ...prev,
          isProcessing: false,
          progress: 100,
          message: 'Processing completed successfully!',
          outputFiles: outputFiles,
        }));

        if (options.onComplete) {
          options.onComplete(outputFiles);
        }

        return outputFiles;
      } else if (completedTask.status === 'failed') {
        const errorMessage = completedTask.errorMessage || 'Processing failed';
        throw new Error(errorMessage);
      } else {
        // This shouldn't happen, but handle unexpected status
        throw new Error(`Unexpected task status: ${completedTask.status}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: errorMessage,
        message: 'Processing failed',
      }));

      if (options.onError) {
        options.onError(errorMessage);
      }

      throw error;
    }
  }, [ensureSession, addTask, pollTaskStatus, resetState]);

  // Specific tool methods with enhanced error handling
  const mergePDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'merge', (files, sessionId, params) => apiClient.mergePDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF merge';
      console.error('Merge PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const compressPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'compress', apiClient.compressPDF, parameters, options);
  }, [processFiles]);

  const splitPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'split', (files, sessionId, params) => apiClient.splitPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF split';
      console.error('Split PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const convertPDFToWord = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'pdf_to_word', apiClient.convertPDFToWord, parameters, options);
  }, [processFiles]);

  const convertPDFToImage = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'pdf_to_image', apiClient.convertPDFToImage, parameters, options);
  }, [processFiles]);

  const convertImageToPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'image_to_pdf', apiClient.convertImageToPDF, parameters, options);
  }, [processFiles]);

  const protectPDF = useCallback((files: File[], parameters: any, options: ProcessingOptions = {}) => {
    return processFiles(files, 'protect', apiClient.protectPDF, parameters, options);
  }, [processFiles]);

  const unlockPDF = useCallback((files: File[], parameters: any, options: ProcessingOptions = {}) => {
    return processFiles(files, 'unlock', apiClient.unlockPDF, parameters, options);
  }, [processFiles]);

  const addWatermark = useCallback((files: File[], parameters: any, options: ProcessingOptions = {}) => {
    return processFiles(files, 'watermark', apiClient.addWatermark, parameters, options);
  }, [processFiles]);

  const rotatePDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'rotate', apiClient.rotatePDF, parameters, options);
  }, [processFiles]);

  const ocrPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'ocr', apiClient.ocrPDF, parameters, options);
  }, [processFiles]);

  const repairPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'repair', (files, sessionId, params) => apiClient.repairPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF repair';
      console.error('Repair PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const organizePDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'organize', apiClient.organizePDF, parameters, options);
  }, [processFiles]);

  const cropPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'crop', apiClient.cropPDF, parameters, options);
  }, [processFiles]);

  const addPageNumbers = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'page_numbers', apiClient.addPageNumbers, parameters, options);
  }, [processFiles]);

  const redactPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'redact', apiClient.redactPDF, parameters, options);
  }, [processFiles]);

  const convertWordToPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'word_to_pdf', apiClient.convertWordToPDF, parameters, options);
  }, [processFiles]);

  const convertExcelToPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'excel_to_pdf', apiClient.convertExcelToPDF, parameters, options);
  }, [processFiles]);

  const convertHTMLToPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'html_to_pdf', apiClient.convertHTMLToPDF, parameters, options);
  }, [processFiles]);

  const comparePDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'compare', apiClient.comparePDF, parameters, options);
  }, [processFiles]);

  const signPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'sign', apiClient.signPDF, parameters, options);
  }, [processFiles]);

  const convertPDFToExcel = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'pdf_to_excel', apiClient.convertPDFToExcel, parameters, options);
  }, [processFiles]);

  const convertPowerPointToPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'powerpoint_to_pdf', apiClient.convertPowerPointToPDF, parameters, options);
  }, [processFiles]);

  const convertPDFToPowerPoint = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'pdf_to_powerpoint', apiClient.convertPDFToPowerPoint, parameters, options);
  }, [processFiles]);

  const convertPDFToPDFA = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'pdf_to_pdfa', apiClient.convertPDFToPDFA, parameters, options);
  }, [processFiles]);

  const editPDF = useCallback((files: File[], parameters: any, options: ProcessingOptions = {}) => {
    return processFiles(files, 'edit', apiClient.editPDF, parameters, options);
  }, [processFiles]);

  const scanToPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'scan_to_pdf', apiClient.scanToPDF, parameters, options);
  }, [processFiles]);

  return {
    // State
    ...state,
    
    // Actions
    resetState,
    
    // Tool methods
    mergePDF,
    compressPDF,
    splitPDF,
    convertPDFToWord,
    convertPDFToImage,
    convertImageToPDF,
    protectPDF,
    unlockPDF,
    addWatermark,
    rotatePDF,
    ocrPDF,
    repairPDF,
    organizePDF,
    cropPDF,
    addPageNumbers,
    redactPDF,
    convertWordToPDF,
    convertExcelToPDF,
    convertHTMLToPDF,
    comparePDF,
    signPDF,
    convertPDFToExcel,
    convertPowerPointToPDF,
    convertPDFToPowerPoint,
    convertPDFToPDFA,
    editPDF,
    scanToPDF,
  };
};

export default usePDFProcessor;
