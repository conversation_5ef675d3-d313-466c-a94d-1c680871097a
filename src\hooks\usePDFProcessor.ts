/**
 * Custom hook for PDF processing operations
 * Provides a unified interface for all PDF tools
 */

import { useState, useCallback } from 'react';
import { useSession } from '../contexts/SessionContext';
import { apiClient, ProcessingResponse } from '../services/api';

// Enhanced API client validation with retry mechanism
const waitForApiClient = async (maxRetries = 5, delay = 200): Promise<void> => {
  for (let i = 0; i < maxRetries; i++) {
    if (apiClient &&
        typeof apiClient === 'object' &&
        typeof apiClient.createSession === 'function' &&
        typeof apiClient.splitPDF === 'function') {
      return;
    }
    await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
  }
  throw new Error('API Client failed to initialize after multiple attempts. Please refresh the page.');
};

export interface ProcessingState {
  isProcessing: boolean;
  progress: number;
  message: string;
  error: string | null;
  taskId: string | null;
  outputFiles: string[] | null;
}

export interface ProcessingOptions {
  onProgress?: (progress: number, message: string) => void;
  onComplete?: (outputFiles: string[]) => void;
  onError?: (error: string) => void;
}

export const usePDFProcessor = () => {
  const { ensureSession, addTask, pollTaskStatus, createSession } = useSession();
  
  const [state, setState] = useState<ProcessingState>({
    isProcessing: false,
    progress: 0,
    message: '',
    error: null,
    taskId: null,
    outputFiles: null,
  });

  const resetState = useCallback(() => {
    setState({
      isProcessing: false,
      progress: 0,
      message: '',
      error: null,
      taskId: null,
      outputFiles: null,
    });
  }, []);

  const processFiles = useCallback(async (
    files: File[],
    toolName: string,
    apiMethod: (files: File[], sessionId: string, parameters?: any) => Promise<ProcessingResponse>,
    parameters: any = {},
    options: ProcessingOptions = {}
  ): Promise<string[]> => {
    resetState();
    
    try {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        message: 'Initializing...',
      }));

      // Wait for API client to be ready
      await waitForApiClient();

      setState(prev => ({
        ...prev,
        message: 'Creating session...',
        progress: 5,
      }));

      // Ensure we have a valid session
      const session = await ensureSession();
      let sessionId = session.sessionId;

      // Validate session exists on backend
      setState(prev => ({
        ...prev,
        message: 'Validating session...',
        progress: 8,
      }));

      try {
        await apiClient.getSessionStatus(sessionId);
        console.log('Session validation successful:', sessionId);
      } catch (sessionError) {
        console.warn('Session validation failed, creating new session:', sessionError);
        // Clear the invalid session and create a new one
        try {
          const newSession = await createSession();
          sessionId = newSession.sessionId;
          console.log('New session created:', sessionId);
        } catch (createError) {
          console.error('Failed to create new session:', createError);
          throw new Error('Unable to establish session. Please refresh the page and try again.');
        }
      }

      setState(prev => ({
        ...prev,
        message: 'Starting processing...',
        progress: 10,
      }));

      // Start processing with validated session
      const response = await apiMethod(files, sessionId, parameters);
      
      setState(prev => ({
        ...prev,
        taskId: response.task_id,
        message: response.message,
        progress: 20,
      }));

      // Add task to session
      addTask(response.task_id, toolName);

      // Poll for completion
      const completedTask = await pollTaskStatus(response.task_id, (task) => {
        setState(prev => ({
          ...prev,
          progress: task.progress || prev.progress,
          message: task.message || prev.message,
        }));
        
        if (options.onProgress) {
          options.onProgress(task.progress || 0, task.message || '');
        }
      });

      if (completedTask.status === 'completed') {
        // Task completed successfully
        const outputFiles = completedTask.outputFiles || [];

        setState(prev => ({
          ...prev,
          isProcessing: false,
          progress: 100,
          message: 'Processing completed successfully!',
          outputFiles: outputFiles,
        }));

        if (options.onComplete) {
          options.onComplete(outputFiles);
        }

        return outputFiles;
      } else if (completedTask.status === 'failed') {
        const errorMessage = completedTask.errorMessage || 'Processing failed';
        throw new Error(errorMessage);
      } else {
        // This shouldn't happen, but handle unexpected status
        throw new Error(`Unexpected task status: ${completedTask.status}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: errorMessage,
        message: 'Processing failed',
      }));

      if (options.onError) {
        options.onError(errorMessage);
      }

      throw error;
    }
  }, [ensureSession, addTask, pollTaskStatus, resetState]);

  // Specific tool methods with enhanced error handling
  const mergePDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'merge', (files, sessionId, params) => apiClient.mergePDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF merge';
      console.error('Merge PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const compressPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'compress', (files, sessionId, params) => apiClient.compressPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF compression';
      console.error('Compress PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const splitPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'split', (files, sessionId, params) => apiClient.splitPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF split';
      console.error('Split PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const convertPDFToWord = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'pdf_to_word', (files, sessionId, params) => apiClient.convertPDFToWord(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF to Word conversion';
      console.error('PDF to Word conversion failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const convertPDFToImage = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'pdf_to_image', (files, sessionId, params) => apiClient.convertPDFToImage(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF to Image conversion';
      console.error('PDF to Image conversion failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const convertImageToPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'image_to_pdf', (files, sessionId, params) => apiClient.convertImageToPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during Image to PDF conversion';
      console.error('Image to PDF conversion failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const protectPDF = useCallback(async (files: File[], parameters: any, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'protect', (files, sessionId, params) => apiClient.protectPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF protection';
      console.error('Protect PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const unlockPDF = useCallback(async (files: File[], parameters: any, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'unlock', (files, sessionId, params) => apiClient.unlockPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF unlocking';
      console.error('Unlock PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const addWatermark = useCallback(async (files: File[], parameters: any, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'watermark', (files, sessionId, params) => apiClient.addWatermark(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during watermark addition';
      console.error('Add Watermark failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const rotatePDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'rotate', (files, sessionId, params) => apiClient.rotatePDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF rotation';
      console.error('Rotate PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const ocrPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'ocr', (files, sessionId, params) => apiClient.ocrPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during OCR processing';
      console.error('OCR PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const repairPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'repair', (files, sessionId, params) => apiClient.repairPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF repair';
      console.error('Repair PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const organizePDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'organize', (files, sessionId, params) => apiClient.organizePDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF organization';
      console.error('Organize PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const cropPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'crop', (files, sessionId, params) => apiClient.cropPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF cropping';
      console.error('Crop PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const addPageNumbers = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'page_numbers', (files, sessionId, params) => apiClient.addPageNumbers(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during page number addition';
      console.error('Add Page Numbers failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const redactPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'redact', (files, sessionId, params) => apiClient.redactPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF redaction';
      console.error('Redact PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const convertWordToPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'word_to_pdf', (files, sessionId, params) => apiClient.convertWordToPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during Word to PDF conversion';
      console.error('Word to PDF conversion failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const convertExcelToPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'excel_to_pdf', (files, sessionId, params) => apiClient.convertExcelToPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during Excel to PDF conversion';
      console.error('Excel to PDF conversion failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const convertHTMLToPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'html_to_pdf', (files, sessionId, params) => apiClient.convertHTMLToPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during HTML to PDF conversion';
      console.error('HTML to PDF conversion failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const comparePDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'compare', (files, sessionId, params) => apiClient.comparePDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF comparison';
      console.error('Compare PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const signPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'sign', (files, sessionId, params) => apiClient.signPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF signing';
      console.error('Sign PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const convertPDFToExcel = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'pdf_to_excel', (files, sessionId, params) => apiClient.convertPDFToExcel(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF to Excel conversion';
      console.error('PDF to Excel conversion failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const convertPowerPointToPDF = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'powerpoint_to_pdf', (files, sessionId, params) => apiClient.convertPowerPointToPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PowerPoint to PDF conversion';
      console.error('PowerPoint to PDF conversion failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const convertPDFToPowerPoint = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'pdf_to_powerpoint', (files, sessionId, params) => apiClient.convertPDFToPowerPoint(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF to PowerPoint conversion';
      console.error('PDF to PowerPoint conversion failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const convertPDFToPDFA = useCallback(async (files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'pdf_to_pdfa', (files, sessionId, params) => apiClient.convertPDFToPDFA(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF to PDF/A conversion';
      console.error('PDF to PDF/A conversion failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const editPDF = useCallback(async (files: File[], parameters: any, options: ProcessingOptions = {}) => {
    try {
      return await processFiles(files, 'edit', (files, sessionId, params) => apiClient.editPDF(files, sessionId, params), parameters, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during PDF editing';
      console.error('Edit PDF failed:', error);
      throw new Error(errorMessage);
    }
  }, [processFiles]);

  const scanToPDF = useCallback((files: File[], parameters: any = {}, options: ProcessingOptions = {}) => {
    return processFiles(files, 'scan_to_pdf', apiClient.scanToPDF, parameters, options);
  }, [processFiles]);

  return {
    // State
    ...state,
    
    // Actions
    resetState,
    
    // Tool methods
    mergePDF,
    compressPDF,
    splitPDF,
    convertPDFToWord,
    convertPDFToImage,
    convertImageToPDF,
    protectPDF,
    unlockPDF,
    addWatermark,
    rotatePDF,
    ocrPDF,
    repairPDF,
    organizePDF,
    cropPDF,
    addPageNumbers,
    redactPDF,
    convertWordToPDF,
    convertExcelToPDF,
    convertHTMLToPDF,
    comparePDF,
    signPDF,
    convertPDFToExcel,
    convertPowerPointToPDF,
    convertPDFToPowerPoint,
    convertPDFToPDFA,
    editPDF,
    scanToPDF,
  };
};

export default usePDFProcessor;
