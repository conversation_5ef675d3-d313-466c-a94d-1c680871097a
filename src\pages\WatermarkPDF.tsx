import React, { useState } from 'react';
import { FileText, Download, ArrowRight, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';
import { useAuth } from '../contexts/AuthContext';

const WatermarkPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [watermarkType, setWatermarkType] = useState<'text' | 'image'>('text');
  const [watermarkText, setWatermarkText] = useState('');
  const [position, setPosition] = useState('center');
  const [opacity, setOpacity] = useState(50);
  const [transparency, setTransparency] = useState(50);
  const [fontSize, setFontSize] = useState(36);
  const [color, setColor] = useState('#808080');
  const [pages, setPages] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { downloadAllFiles } = useSession();
  const { user } = useAuth();

  const {
    isProcessing,
    progress,
    message,
    error,
    outputFiles,
    taskId,
    addWatermark,
    resetState
  } = usePDFProcessor();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleWatermark = async () => {
    if (files.length === 0 || !watermarkText) return;

    try {
      const parameters = {
        watermark_text: watermarkText,
        position: position,
        opacity: opacity / 100,
        font_size: fontSize,
        color: color,
        pages: pages || undefined,
      };

      await addWatermark(files, parameters, {
        onProgress: (progress, message) => {
          console.log(`Progress: ${progress}% - ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Watermark completed:', outputFiles);
        },
        onError: (error) => {
          console.error('Watermark failed:', error);
        }
      });
    } catch (err) {
      console.error('Watermark operation failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="Filigrane"
      description="Choisissez une image ou un texte à appliquer sur votre PDF. Sélectionnez l'emplacement, la transparence et la typographie"
      icon={<FileText className="w-8 h-8" />}
      color="from-lime-500 to-lime-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Configuration du filigrane
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Type de filigrane
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="watermarkType"
                      value="text"
                      checked={watermarkType === 'text'}
                      onChange={(e) => setWatermarkType(e.target.value as 'text')}
                      className="text-lime-600"
                    />
                    <span className="text-slate-700">Texte</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="watermarkType"
                      value="image"
                      checked={watermarkType === 'image'}
                      onChange={(e) => setWatermarkType(e.target.value as 'image')}
                      className="text-lime-600"
                    />
                    <span className="text-slate-700">Image</span>
                  </label>
                </div>
              </div>

              {watermarkType === 'text' && (
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Texte du filigrane
                  </label>
                  <input
                    type="text"
                    placeholder="Entrez votre texte"
                    value={watermarkText}
                    onChange={(e) => setWatermarkText(e.target.value)}
                    className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
                  />
                </div>
              )}

              {watermarkType === 'image' && (
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Image du filigrane
                  </label>
                  <input
                    type="file"
                    accept=".png,.jpg,.jpeg"
                    className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Transparence: {transparency}%
                </label>
                <input
                  type="range"
                  min="10"
                  max="90"
                  value={transparency}
                  onChange={(e) => setTransparency(Number(e.target.value))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Position
                </label>
                <select
                  value={position}
                  onChange={(e) => setPosition(e.target.value)}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
                >
                  <option value="center">Centre</option>
                  <option value="top-left">Haut gauche</option>
                  <option value="top-right">Haut droite</option>
                  <option value="bottom-left">Bas gauche</option>
                  <option value="bottom-right">Bas droite</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-lime-50 border border-lime-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-lime-600"></div>
              <span className="text-lime-800 font-medium">Ajout du filigrane en cours...</span>
            </div>
            <div className="w-full bg-lime-200 rounded-full h-2 mb-2">
              <div
                className="bg-lime-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-lime-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de l'ajout du filigrane</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Filigrane ajouté avec succès!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) traité(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {files.length > 0 && watermarkText && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleWatermark}
              disabled={isProcessing}
              className="bg-gradient-to-r from-lime-600 to-green-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Ajout du filigrane...</span>
                </>
              ) : (
                <>
                  <span>Ajouter le filigrane</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default WatermarkPDF;