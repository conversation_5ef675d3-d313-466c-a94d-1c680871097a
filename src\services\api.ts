/**
 * Main API service for PDF processing tools
 * Connects React frontend to Python backend
 */

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
const API_TIMEOUT = 300000; // 5 minutes

// Types
export interface ProcessingResponse {
  task_id: string;
  status: 'processing' | 'completed' | 'failed';
  message: string;
  session_id: string;
  created_at: string;
  output_files?: string[];
  error_message?: string;
  processing_time?: number;
}

export interface SessionResponse {
  session_id: string;
  created_at: string;
  last_activity: string;
  active_tasks: number;
}

export interface TaskStatusResponse {
  task_id: string;
  status: 'processing' | 'completed' | 'failed';
  progress?: number;
  message?: string;
  output_files?: string[];
  error_message?: string;
  processing_time?: number;
}

// API Client Class
class APIClient {
  private baseURL: string;
  private timeout: number;

  constructor(baseURL: string = API_BASE_URL, timeout: number = API_TIMEOUT) {
    this.baseURL = baseURL;
    this.timeout = timeout;
  }

  // Helper method to create FormData
  private createFormData(files: File[], sessionId: string, parameters: Record<string, any> = {}): FormData {
    console.log('createFormData called with:', { filesCount: files.length, sessionId, parameters });

    if (!files || files.length === 0) {
      throw new Error('No files provided for form data creation');
    }

    if (!sessionId) {
      throw new Error('Session ID is required for form data creation');
    }

    const formData = new FormData();

    // Add session ID
    formData.append('session_id', sessionId);

    // Add files
    files.forEach((file, index) => {
      console.log(`Adding file ${index + 1}: ${file.name}`);
      formData.append('files', file);
    });

    // Add parameters
    Object.entries(parameters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        console.log(`Adding parameter: ${key} = ${value}`);
        formData.append(key, String(value));
      }
    });

    console.log('FormData created successfully');
    return formData;
  }

  // Generic API request method
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    
    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          ...options.headers,
        },
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Request timeout - the operation took too long to complete');
        }
        throw error;
      }
      
      throw new Error('An unexpected error occurred');
    }
  }

  // Session Management
  async createSession(): Promise<SessionResponse> {
    return this.request<SessionResponse>('/api/tools/session', {
      method: 'POST',
    });
  }

  async getSessionStatus(sessionId: string): Promise<any> {
    return this.request(`/api/tools/session/${sessionId}`);
  }

  // Task Management
  async getTaskStatus(taskId: string): Promise<TaskStatusResponse> {
    return this.request<TaskStatusResponse>(`/api/tools/task/${taskId}`);
  }

  async downloadFile(taskId: string, filename: string): Promise<Blob> {
    const url = `${this.baseURL}/api/tools/download/${taskId}`;

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Download failed: ${response.statusText}`);
    }

    return response.blob();
  }

  // PDF Processing Tools
  async mergePDF(files: File[], sessionId: string, parameters: {
    bookmark_titles?: string[];
    add_bookmarks?: boolean;
  } = {}): Promise<ProcessingResponse> {
    console.log('mergePDF API method called with:', {
      filesCount: files.length,
      sessionId,
      parameters,
      hasCreateFormData: typeof this.createFormData === 'function'
    });

    if (!files || files.length === 0) {
      throw new Error('No files provided for PDF merge');
    }

    if (files.length < 2) {
      throw new Error('At least 2 PDF files are required for merging');
    }

    if (!sessionId) {
      throw new Error('Session ID is required for PDF merge');
    }

    try {
      const formData = this.createFormData(files, sessionId, parameters);

      console.log('Sending merge request to /api/tools/merge');
      return this.request<ProcessingResponse>('/api/tools/merge', {
        method: 'POST',
        body: formData,
      });
    } catch (error) {
      console.error('Error in mergePDF:', error);
      throw error;
    }
  }

  async compressPDF(files: File[], sessionId: string, parameters: {
    compression_level?: 'low' | 'medium' | 'high';
    image_quality?: number;
    remove_metadata?: boolean;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);
    
    return this.request<ProcessingResponse>('/api/tools/compress', {
      method: 'POST',
      body: formData,
    });
  }

  async splitPDF(files: File[], sessionId: string, parameters: {
    split_method?: 'pages' | 'ranges' | 'size' | 'bookmarks' | 'extract';
    pages_per_split?: number;
    page_ranges?: string;
    max_size_mb?: number;
    extract_pages?: string;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);
    
    return this.request<ProcessingResponse>('/api/tools/split', {
      method: 'POST',
      body: formData,
    });
  }

  async convertPDFToWord(files: File[], sessionId: string, parameters: {
    extract_images?: boolean;
    preserve_layout?: boolean;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);
    
    return this.request<ProcessingResponse>('/api/tools/convert/pdf-to-word', {
      method: 'POST',
      body: formData,
    });
  }

  async convertPDFToImage(files: File[], sessionId: string, parameters: {
    output_format?: 'jpg' | 'png';
    dpi?: number;
    pages?: string;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);
    
    return this.request<ProcessingResponse>('/api/tools/convert/pdf-to-image', {
      method: 'POST',
      body: formData,
    });
  }

  async convertImageToPDF(files: File[], sessionId: string, parameters: {
    page_size?: string;
    orientation?: 'auto' | 'portrait' | 'landscape';
    margin?: number;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);
    
    return this.request<ProcessingResponse>('/api/tools/convert/image-to-pdf', {
      method: 'POST',
      body: formData,
    });
  }

  async protectPDF(files: File[], sessionId: string, parameters: {
    user_password: string;
    owner_password?: string;
    allow_printing?: boolean;
    allow_copying?: boolean;
    allow_modification?: boolean;
  }): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);
    
    return this.request<ProcessingResponse>('/api/tools/protect', {
      method: 'POST',
      body: formData,
    });
  }

  async unlockPDF(files: File[], sessionId: string, parameters: {
    password: string;
  }): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);
    
    return this.request<ProcessingResponse>('/api/tools/unlock', {
      method: 'POST',
      body: formData,
    });
  }

  async addWatermark(files: File[], sessionId: string, parameters: {
    watermark_text: string;
    position?: string;
    opacity?: number;
    font_size?: number;
    color?: string;
    pages?: string;
  }): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);
    
    return this.request<ProcessingResponse>('/api/tools/watermark', {
      method: 'POST',
      body: formData,
    });
  }

  async rotatePDF(files: File[], sessionId: string, parameters: {
    rotation?: number;
    pages?: string;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);
    
    return this.request<ProcessingResponse>('/api/tools/rotate', {
      method: 'POST',
      body: formData,
    });
  }

  // OCR Tool
  async ocrPDF(files: File[], sessionId: string, parameters: {
    language?: string;
    output_format?: string;
    dpi?: number;
    preprocessing?: boolean;
    confidence_threshold?: number;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/ocr', {
      method: 'POST',
      body: formData,
    });
  }

  // Repair Tool
  async repairPDF(files: File[], sessionId: string, parameters: {
    repair_strategy?: string;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/repair', {
      method: 'POST',
      body: formData,
    });
  }

  // Organize Tool
  async organizePDF(files: File[], sessionId: string, parameters: {
    operation?: string;
    page_order?: string;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/organize', {
      method: 'POST',
      body: formData,
    });
  }

  // Crop Tool
  async cropPDF(files: File[], sessionId: string, parameters: {
    crop_mode?: string;
    margin_threshold?: number;
    pages?: string;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/crop', {
      method: 'POST',
      body: formData,
    });
  }

  // Page Numbers Tool
  async addPageNumbers(files: File[], sessionId: string, parameters: {
    position?: string;
    format_style?: string;
    start_number?: number;
    font_size?: number;
    pages?: string;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/page-numbers', {
      method: 'POST',
      body: formData,
    });
  }

  // Redact Tool
  async redactPDF(files: File[], sessionId: string, parameters: {
    patterns?: string;
    method?: string;
    pages?: string;
    case_sensitive?: boolean;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/redact', {
      method: 'POST',
      body: formData,
    });
  }

  // Word to PDF Tool
  async convertWordToPDF(files: File[], sessionId: string, parameters: {
    quality?: string;
    preserve_layout?: boolean;
    include_metadata?: boolean;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/convert/word-to-pdf', {
      method: 'POST',
      body: formData,
    });
  }

  // Excel to PDF Tool
  async convertExcelToPDF(files: File[], sessionId: string, parameters: {
    orientation?: string;
    sheets?: string;
    fit_to_page?: boolean;
    include_gridlines?: boolean;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/convert/excel-to-pdf', {
      method: 'POST',
      body: formData,
    });
  }

  // HTML to PDF Tool
  async convertHTMLToPDF(files: File[], sessionId: string, parameters: {
    page_size?: string;
    orientation?: string;
    margin_top?: string;
    margin_bottom?: string;
    margin_left?: string;
    margin_right?: string;
    include_background?: boolean;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/convert/html-to-pdf', {
      method: 'POST',
      body: formData,
    });
  }

  // Compare Tool
  async comparePDF(files: File[], sessionId: string, parameters: {
    method?: string;
    sensitivity?: string;
    highlight_color?: string;
    generate_report?: boolean;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/compare', {
      method: 'POST',
      body: formData,
    });
  }

  // Sign Tool
  async signPDF(files: File[], sessionId: string, parameters: {
    signature_type?: string;
    signature_text?: string;
    position?: string;
    pages?: string;
    signer_name?: string;
    reason?: string;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/sign', {
      method: 'POST',
      body: formData,
    });
  }

  // PDF to Excel Tool
  async convertPDFToExcel(files: File[], sessionId: string, parameters: {
    output_format?: string;
    extraction_method?: string;
    pages?: string;
    include_headers?: boolean;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/convert/pdf-to-excel', {
      method: 'POST',
      body: formData,
    });
  }

  // PowerPoint to PDF Tool
  async convertPowerPointToPDF(files: File[], sessionId: string, parameters: {
    quality?: string;
    include_notes?: boolean;
    slides_per_page?: number;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/convert/powerpoint-to-pdf', {
      method: 'POST',
      body: formData,
    });
  }

  // PDF to PowerPoint Tool
  async convertPDFToPowerPoint(files: File[], sessionId: string, parameters: {
    output_format?: string;
    conversion_method?: string;
    pages?: string;
    slide_layout?: string;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/convert/pdf-to-powerpoint', {
      method: 'POST',
      body: formData,
    });
  }

  // PDF to PDF/A Tool
  async convertPDFToPDFA(files: File[], sessionId: string, parameters: {
    conformance_level?: string;
    color_profile?: string;
    embed_fonts?: boolean;
    optimize_images?: boolean;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/convert/pdf-to-pdfa', {
      method: 'POST',
      body: formData,
    });
  }

  // Edit PDF Tool
  async editPDF(files: File[], sessionId: string, parameters: {
    operations: string;
    pages?: string;
  }): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/edit', {
      method: 'POST',
      body: formData,
    });
  }

  // Scan to PDF Tool
  async scanToPDF(files: File[], sessionId: string, parameters: {
    ocr_enabled?: boolean;
    ocr_language?: string;
    page_size?: string;
    combine_pages?: boolean;
    dpi?: number;
    enhance_image?: boolean;
  } = {}): Promise<ProcessingResponse> {
    const formData = this.createFormData(files, sessionId, parameters);

    return this.request<ProcessingResponse>('/api/tools/scan-to-pdf', {
      method: 'POST',
      body: formData,
    });
  }

  // Information endpoints
  async getCompressionLevels(): Promise<any> {
    return this.request('/api/tools/compress/levels');
  }

  async getSplitOptions(): Promise<any> {
    return this.request('/api/tools/split/options');
  }

  async getOCRLanguages(): Promise<any> {
    return this.request('/api/tools/ocr/languages');
  }

  async getRepairStrategies(): Promise<any> {
    return this.request('/api/tools/repair/strategies');
  }

  async getOrganizeOperations(): Promise<any> {
    return this.request('/api/tools/organize/operations');
  }

  async getCropModes(): Promise<any> {
    return this.request('/api/tools/crop/modes');
  }

  async getPageNumberFormats(): Promise<any> {
    return this.request('/api/tools/page-numbers/formats');
  }

  async getRedactionPatterns(): Promise<any> {
    return this.request('/api/tools/redact/patterns');
  }

  async getWordToPDFSupport(): Promise<any> {
    return this.request('/api/tools/convert/word-to-pdf/support');
  }

  async getExcelToPDFOptions(): Promise<any> {
    return this.request('/api/tools/convert/excel-to-pdf/options');
  }

  async getHTMLToPDFSupport(): Promise<any> {
    return this.request('/api/tools/convert/html-to-pdf/support');
  }

  async getCompareOptions(): Promise<any> {
    return this.request('/api/tools/compare/options');
  }

  async getSignatureOptions(): Promise<any> {
    return this.request('/api/tools/sign/options');
  }

  async getPDFToExcelOptions(): Promise<any> {
    return this.request('/api/tools/convert/pdf-to-excel/options');
  }

  async getPowerPointToPDFSupport(): Promise<any> {
    return this.request('/api/tools/convert/powerpoint-to-pdf/support');
  }

  async getPDFToPowerPointOptions(): Promise<any> {
    return this.request('/api/tools/convert/pdf-to-powerpoint/options');
  }

  async getPDFToPDFAOptions(): Promise<any> {
    return this.request('/api/tools/convert/pdf-to-pdfa/options');
  }

  async getEditOperations(): Promise<any> {
    return this.request('/api/tools/edit/operations');
  }

  async getScanToPDFOptions(): Promise<any> {
    return this.request('/api/tools/scan-to-pdf/options');
  }
}

// Create and export API client instance
export const apiClient = new APIClient();

// Add debugging for API client initialization
console.log('API Client initialized:', {
  hasCreateSession: typeof apiClient.createSession === 'function',
  hasSplitPDF: typeof apiClient.splitPDF === 'function',
  hasGetSessionStatus: typeof apiClient.getSessionStatus === 'function',
  apiClientType: typeof apiClient
});

// Export default
export default apiClient;
