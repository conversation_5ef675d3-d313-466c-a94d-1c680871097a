"""
PDF split tool for dividing PDF files into multiple parts.
"""
import os
import re
from typing import List, Dict, Any, Optional, Tuple
from PyPDF2 import PdfWriter, PdfReader
import structlog

from .base_tool import Base<PERSON>FTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFSplitTool(BasePDFTool):
    """Tool for splitting PDF files into multiple parts."""
    
    def __init__(self):
        super().__init__("split")
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Split PDF files into multiple parts.
        
        Args:
            input_files: List of PDF file paths to split (typically one file)
            output_path: Output directory for split files
            parameters: Split parameters (type, ranges, pages, etc.)
            
        Returns:
            List containing paths to split PDF files
        """
        # Validate inputs
        if len(input_files) != 1:
            raise ValidationError("PDF split requires exactly 1 input file")
        
        input_file = input_files[0]
        self.validate_input_files([input_file])
        self.validate_pdf_files([input_file])
        
        # Prepare parameters
        params = parameters or {}
        split_type = params.get("split_type", "pages")
        split_value = params.get("split_value", "1")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        try:
            # Read the PDF
            with open(input_file, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                # Check if PDF is encrypted
                if pdf_reader.is_encrypted:
                    raise ProcessingError("Cannot split encrypted PDF")
                
                total_pages = len(pdf_reader.pages)
                
                self.logger.info(
                    "Starting PDF split",
                    input_file=input_file,
                    total_pages=total_pages,
                    split_type=split_type,
                    split_value=split_value
                )
                
                # Determine split strategy
                if split_type == "pages":
                    output_files = await self._split_by_pages(
                        pdf_reader, input_file, output_path, int(split_value)
                    )
                elif split_type == "ranges":
                    output_files = await self._split_by_ranges(
                        pdf_reader, input_file, output_path, split_value
                    )
                elif split_type == "size":
                    output_files = await self._split_by_size(
                        pdf_reader, input_file, output_path, split_value
                    )
                elif split_type == "bookmarks":
                    output_files = await self._split_by_bookmarks(
                        pdf_reader, input_file, output_path
                    )
                elif split_type == "extract":
                    output_files = await self._extract_pages(
                        pdf_reader, input_file, output_path, split_value
                    )
                else:
                    raise ValidationError(f"Invalid split type: {split_type}")
                
                self.logger.info(
                    "PDF split completed successfully",
                    input_file=input_file,
                    output_files=len(output_files),
                    total_pages=total_pages
                )
                
                return output_files
                
        except Exception as e:
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF split: {str(e)}")
    
    async def _split_by_pages(
        self, 
        pdf_reader: PdfReader, 
        input_file: str, 
        output_path: str, 
        pages_per_file: int
    ) -> List[str]:
        """Split PDF by number of pages per file."""
        if pages_per_file <= 0:
            raise ValidationError("Pages per file must be greater than 0")
        
        total_pages = len(pdf_reader.pages)
        output_files = []
        
        for start_page in range(0, total_pages, pages_per_file):
            end_page = min(start_page + pages_per_file, total_pages)
            
            # Create output filename
            file_num = (start_page // pages_per_file) + 1
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            output_filename = f"{base_name}_part_{file_num:03d}.pdf"
            output_file = os.path.join(output_path, output_filename)
            
            # Create PDF writer for this part
            pdf_writer = PdfWriter()
            
            # Add pages to this part
            for page_num in range(start_page, end_page):
                pdf_writer.add_page(pdf_reader.pages[page_num])
            
            # Write the file
            with open(output_file, 'wb') as output_pdf:
                pdf_writer.write(output_pdf)
            
            output_files.append(output_file)
            
            self.logger.debug(
                f"Created part {file_num}",
                output_file=output_file,
                pages=f"{start_page + 1}-{end_page}"
            )
        
        return output_files
    
    async def _split_by_ranges(
        self, 
        pdf_reader: PdfReader, 
        input_file: str, 
        output_path: str, 
        ranges_str: str
    ) -> List[str]:
        """Split PDF by specified page ranges."""
        ranges = self._parse_page_ranges(ranges_str, len(pdf_reader.pages))
        output_files = []
        
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        
        for i, (start, end) in enumerate(ranges, 1):
            # Create output filename
            output_filename = f"{base_name}_range_{i:03d}_pages_{start}-{end}.pdf"
            output_file = os.path.join(output_path, output_filename)
            
            # Create PDF writer for this range
            pdf_writer = PdfWriter()
            
            # Add pages in this range (convert to 0-based indexing)
            for page_num in range(start - 1, end):
                if page_num < len(pdf_reader.pages):
                    pdf_writer.add_page(pdf_reader.pages[page_num])
            
            # Write the file
            with open(output_file, 'wb') as output_pdf:
                pdf_writer.write(output_pdf)
            
            output_files.append(output_file)
            
            self.logger.debug(
                f"Created range {i}",
                output_file=output_file,
                pages=f"{start}-{end}"
            )
        
        return output_files
    
    async def _split_by_size(
        self, 
        pdf_reader: PdfReader, 
        input_file: str, 
        output_path: str, 
        target_size: str
    ) -> List[str]:
        """Split PDF by target file size."""
        # Parse target size (e.g., "5MB", "1GB")
        size_bytes = self._parse_size_string(target_size)
        
        output_files = []
        current_writer = PdfWriter()
        current_size = 0
        file_num = 1
        
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        
        for page_num, page in enumerate(pdf_reader.pages):
            # Estimate page size (rough approximation)
            page_size = len(page.extract_text().encode('utf-8')) * 10  # Very rough estimate
            
            # If adding this page would exceed target size, save current file
            if current_size + page_size > size_bytes and len(current_writer.pages) > 0:
                output_filename = f"{base_name}_size_{file_num:03d}.pdf"
                output_file = os.path.join(output_path, output_filename)
                
                with open(output_file, 'wb') as output_pdf:
                    current_writer.write(output_pdf)
                
                output_files.append(output_file)
                
                self.logger.debug(
                    f"Created size-based part {file_num}",
                    output_file=output_file,
                    estimated_size=current_size
                )
                
                # Start new file
                current_writer = PdfWriter()
                current_size = 0
                file_num += 1
            
            # Add page to current file
            current_writer.add_page(page)
            current_size += page_size
        
        # Save the last file if it has pages
        if len(current_writer.pages) > 0:
            output_filename = f"{base_name}_size_{file_num:03d}.pdf"
            output_file = os.path.join(output_path, output_filename)
            
            with open(output_file, 'wb') as output_pdf:
                current_writer.write(output_pdf)
            
            output_files.append(output_file)
        
        return output_files
    
    async def _split_by_bookmarks(
        self, 
        pdf_reader: PdfReader, 
        input_file: str, 
        output_path: str
    ) -> List[str]:
        """Split PDF by bookmarks/outline."""
        if not pdf_reader.outline:
            raise ValidationError("PDF has no bookmarks to split by")
        
        # Extract bookmark information
        bookmarks = self._extract_bookmark_pages(pdf_reader.outline, pdf_reader)
        
        if not bookmarks:
            raise ValidationError("No valid bookmarks found for splitting")
        
        output_files = []
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        
        for i, (title, start_page, end_page) in enumerate(bookmarks, 1):
            # Clean title for filename
            clean_title = re.sub(r'[^\w\s-]', '', title).strip()
            clean_title = re.sub(r'[-\s]+', '_', clean_title)
            
            output_filename = f"{base_name}_{i:03d}_{clean_title}.pdf"
            output_file = os.path.join(output_path, output_filename)
            
            # Create PDF writer for this bookmark section
            pdf_writer = PdfWriter()
            
            # Add pages for this bookmark
            for page_num in range(start_page, min(end_page, len(pdf_reader.pages))):
                pdf_writer.add_page(pdf_reader.pages[page_num])
            
            # Write the file
            with open(output_file, 'wb') as output_pdf:
                pdf_writer.write(output_pdf)
            
            output_files.append(output_file)
            
            self.logger.debug(
                f"Created bookmark section {i}",
                output_file=output_file,
                title=title,
                pages=f"{start_page + 1}-{end_page}"
            )
        
        return output_files
    
    async def _extract_pages(
        self, 
        pdf_reader: PdfReader, 
        input_file: str, 
        output_path: str, 
        pages_str: str
    ) -> List[str]:
        """Extract specific pages from PDF."""
        page_numbers = self._parse_page_list(pages_str, len(pdf_reader.pages))
        
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_filename = f"{base_name}_extracted_pages.pdf"
        output_file = os.path.join(output_path, output_filename)
        
        # Create PDF writer
        pdf_writer = PdfWriter()
        
        # Add specified pages (convert to 0-based indexing)
        for page_num in sorted(page_numbers):
            if 0 <= page_num - 1 < len(pdf_reader.pages):
                pdf_writer.add_page(pdf_reader.pages[page_num - 1])
        
        # Write the file
        with open(output_file, 'wb') as output_pdf:
            pdf_writer.write(output_pdf)
        
        self.logger.debug(
            "Extracted pages",
            output_file=output_file,
            pages=sorted(page_numbers)
        )
        
        return [output_file]

    def _parse_page_ranges(self, ranges_str: str, total_pages: int) -> List[Tuple[int, int]]:
        """Parse page ranges string like '1-5,10-15,20'."""
        ranges = []

        for range_part in ranges_str.split(','):
            range_part = range_part.strip()

            if '-' in range_part:
                # Range like "1-5"
                start_str, end_str = range_part.split('-', 1)
                start = int(start_str.strip())
                end = int(end_str.strip())

                if start < 1 or end > total_pages or start > end:
                    raise ValidationError(f"Invalid page range: {range_part}")

                ranges.append((start, end))
            else:
                # Single page like "10"
                page = int(range_part)

                if page < 1 or page > total_pages:
                    raise ValidationError(f"Invalid page number: {page}")

                ranges.append((page, page))

        return ranges

    def _parse_page_list(self, pages_str: str, total_pages: int) -> List[int]:
        """Parse page list string like '1,3,5-7,10'."""
        pages = set()

        for part in pages_str.split(','):
            part = part.strip()

            if '-' in part:
                # Range like "5-7"
                start_str, end_str = part.split('-', 1)
                start = int(start_str.strip())
                end = int(end_str.strip())

                if start < 1 or end > total_pages or start > end:
                    raise ValidationError(f"Invalid page range: {part}")

                pages.update(range(start, end + 1))
            else:
                # Single page like "1"
                page = int(part)

                if page < 1 or page > total_pages:
                    raise ValidationError(f"Invalid page number: {page}")

                pages.add(page)

        return list(pages)

    def _parse_size_string(self, size_str: str) -> int:
        """Parse size string like '5MB', '1GB' to bytes."""
        size_str = size_str.upper().strip()

        # Extract number and unit
        match = re.match(r'^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$', size_str)
        if not match:
            raise ValidationError(f"Invalid size format: {size_str}")

        number = float(match.group(1))
        unit = match.group(2)

        multipliers = {
            'B': 1,
            'KB': 1024,
            'MB': 1024 * 1024,
            'GB': 1024 * 1024 * 1024
        }

        return int(number * multipliers[unit])

    def _extract_bookmark_pages(self, outline, pdf_reader) -> List[Tuple[str, int, int]]:
        """Extract bookmark information with page ranges."""
        bookmarks = []

        def process_outline_item(item, level=0):
            if hasattr(item, 'title') and hasattr(item, 'page'):
                try:
                    # Get the page number for this bookmark
                    page_num = pdf_reader.get_destination_page_number(item)
                    title = item.title
                    bookmarks.append((title, page_num))
                except Exception:
                    pass

            # Process nested bookmarks
            if hasattr(item, '__iter__'):
                for subitem in item:
                    process_outline_item(subitem, level + 1)

        # Process all outline items
        for item in outline:
            process_outline_item(item)

        # Convert to ranges (start_page, end_page)
        bookmark_ranges = []
        for i, (title, start_page) in enumerate(bookmarks):
            if i + 1 < len(bookmarks):
                end_page = bookmarks[i + 1][1]
            else:
                end_page = len(pdf_reader.pages)

            bookmark_ranges.append((title, start_page, end_page))

        return bookmark_ranges

    def get_split_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available split options and their descriptions."""
        return {
            "pages": {
                "description": "Split by number of pages per file",
                "parameter": "Number of pages per file (e.g., '5')",
                "example": "5"
            },
            "ranges": {
                "description": "Split by specific page ranges",
                "parameter": "Page ranges (e.g., '1-5,10-15,20')",
                "example": "1-5,10-15,20"
            },
            "size": {
                "description": "Split by target file size",
                "parameter": "Target size (e.g., '5MB', '1GB')",
                "example": "5MB"
            },
            "bookmarks": {
                "description": "Split by PDF bookmarks/outline",
                "parameter": "None (uses existing bookmarks)",
                "example": None
            },
            "extract": {
                "description": "Extract specific pages",
                "parameter": "Page numbers (e.g., '1,3,5-7,10')",
                "example": "1,3,5-7,10"
            }
        }

    async def get_pdf_info(self, file_path: str) -> Dict[str, Any]:
        """Get information about a PDF file for split planning."""
        try:
            with open(file_path, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)

                info = {
                    "filename": os.path.basename(file_path),
                    "page_count": len(pdf_reader.pages),
                    "is_encrypted": pdf_reader.is_encrypted,
                    "size_mb": round(self.get_file_size_mb(file_path), 2),
                    "has_bookmarks": bool(pdf_reader.outline)
                }

                # Get bookmark information if available
                if pdf_reader.outline:
                    bookmarks = self._extract_bookmark_pages(pdf_reader.outline, pdf_reader)
                    info["bookmarks"] = [
                        {"title": title, "start_page": start + 1, "end_page": end}
                        for title, start, end in bookmarks
                    ]

                return info

        except Exception as e:
            self.logger.error("Failed to get PDF info", file_path=file_path, error=str(e))
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }


# Create tool instance
split_tool = PDFSplitTool()
