"""
Session management for multi-user concurrent PDF processing.
"""
import os
import uuid
import asyncio
import shutil
from datetime import datetime, timedelta
from typing import Dict, Optional, Set
import structlog
from sqlalchemy.orm import Session

from ..config import settings
from ..database import SessionLocal, ProcessingSession

logger = structlog.get_logger()


class SessionManager:
    """Manages user sessions and temporary directories for concurrent processing."""
    
    def __init__(self):
        self.active_sessions: Dict[str, dict] = {}
        self.cleanup_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
    
    async def create_session(self, user_id: Optional[int] = None) -> str:
        """Create a new processing session."""
        session_id = str(uuid.uuid4())
        
        # Create temporary directory for this session
        session_dir = os.path.join(settings.temp_dir, "sessions", session_id)
        os.makedirs(session_dir, exist_ok=True)
        
        # Create subdirectories for different file types
        os.makedirs(os.path.join(session_dir, "uploads"), exist_ok=True)
        os.makedirs(os.path.join(session_dir, "processing"), exist_ok=True)
        os.makedirs(os.path.join(session_dir, "outputs"), exist_ok=True)
        
        async with self._lock:
            # Store session in memory
            self.active_sessions[session_id] = {
                "user_id": user_id,
                "created_at": datetime.now(),
                "last_activity": datetime.now(),
                "temp_dir": session_dir,
                "active_tasks": set()
            }
        
        # Store session in database
        db = SessionLocal()
        try:
            db_session = ProcessingSession(
                session_id=session_id,
                user_id=user_id,
                temp_dir=session_dir
            )
            db.add(db_session)
            db.commit()
        except Exception as e:
            logger.error("Failed to create database session", session_id=session_id, error=str(e))
        finally:
            db.close()
        
        logger.info("Created new session", session_id=session_id, user_id=user_id)
        return session_id
    
    async def get_session(self, session_id: str) -> Optional[dict]:
        """Get session information."""
        async with self._lock:
            session = self.active_sessions.get(session_id)
            if session:
                # Update last activity
                session["last_activity"] = datetime.now()
                
                # Update database
                db = SessionLocal()
                try:
                    db_session = db.query(ProcessingSession).filter(
                        ProcessingSession.session_id == session_id
                    ).first()
                    if db_session:
                        db_session.last_activity = datetime.now()
                        db.commit()
                except Exception as e:
                    logger.error("Failed to update session activity", session_id=session_id, error=str(e))
                finally:
                    db.close()
            
            return session
    
    async def get_session_dir(self, session_id: str) -> Optional[str]:
        """Get the temporary directory for a session."""
        session = await self.get_session(session_id)
        return session["temp_dir"] if session else None
    
    async def add_task_to_session(self, session_id: str, task_id: str):
        """Add a task to a session's active tasks."""
        async with self._lock:
            session = self.active_sessions.get(session_id)
            if session:
                session["active_tasks"].add(task_id)
                session["last_activity"] = datetime.now()
    
    async def remove_task_from_session(self, session_id: str, task_id: str):
        """Remove a task from a session's active tasks."""
        async with self._lock:
            session = self.active_sessions.get(session_id)
            if session:
                session["active_tasks"].discard(task_id)
                session["last_activity"] = datetime.now()
    
    async def cleanup_session(self, session_id: str):
        """Clean up a session and its temporary files."""
        async with self._lock:
            session = self.active_sessions.get(session_id)
            if not session:
                return
            
            # Check if there are active tasks
            if session["active_tasks"]:
                logger.warning(
                    "Attempting to cleanup session with active tasks",
                    session_id=session_id,
                    active_tasks=len(session["active_tasks"])
                )
                return
            
            # Remove from memory
            del self.active_sessions[session_id]
        
        # Clean up temporary directory
        session_dir = session["temp_dir"]
        if os.path.exists(session_dir):
            try:
                shutil.rmtree(session_dir)
                logger.info("Cleaned up session directory", session_id=session_id, path=session_dir)
            except Exception as e:
                logger.error("Failed to cleanup session directory", session_id=session_id, error=str(e))
        
        # Update database
        db = SessionLocal()
        try:
            db_session = db.query(ProcessingSession).filter(
                ProcessingSession.session_id == session_id
            ).first()
            if db_session:
                db_session.is_active = False
                db.commit()
        except Exception as e:
            logger.error("Failed to update session in database", session_id=session_id, error=str(e))
        finally:
            db.close()
        
        logger.info("Session cleaned up", session_id=session_id)
    
    async def cleanup_expired_sessions(self):
        """Clean up expired sessions."""
        cutoff_time = datetime.now() - timedelta(minutes=settings.session_timeout_minutes)
        expired_sessions = []
        
        async with self._lock:
            for session_id, session in self.active_sessions.items():
                if session["last_activity"] < cutoff_time and not session["active_tasks"]:
                    expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            await self.cleanup_session(session_id)
            logger.info("Cleaned up expired session", session_id=session_id)
    
    async def start_cleanup_task(self):
        """Start the periodic cleanup task."""
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("Started session cleanup task")
    
    async def stop_cleanup_task(self):
        """Stop the periodic cleanup task."""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            self.cleanup_task = None
            logger.info("Stopped session cleanup task")
    
    async def _cleanup_loop(self):
        """Periodic cleanup loop."""
        while True:
            try:
                await asyncio.sleep(settings.cleanup_interval_minutes * 60)
                await self.cleanup_expired_sessions()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in cleanup loop", error=str(e))
    
    async def get_session_stats(self) -> dict:
        """Get statistics about active sessions."""
        async with self._lock:
            total_sessions = len(self.active_sessions)
            total_tasks = sum(len(session["active_tasks"]) for session in self.active_sessions.values())
            
            return {
                "total_sessions": total_sessions,
                "total_active_tasks": total_tasks,
                "sessions": {
                    session_id: {
                        "user_id": session["user_id"],
                        "created_at": session["created_at"].isoformat(),
                        "last_activity": session["last_activity"].isoformat(),
                        "active_tasks": len(session["active_tasks"])
                    }
                    for session_id, session in self.active_sessions.items()
                }
            }
