"""
PDF to Word conversion tool.
"""
import os
import fitz  # PyMuPDF
from typing import List, Dict, Any, Optional
from docx import Document
from docx.shared import Inches
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFToWordTool(BasePDFTool):
    """Tool for converting PDF files to Word documents."""
    
    def __init__(self):
        super().__init__("pdf_to_word")
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Convert PDF files to Word documents.
        
        Args:
            input_files: List of PDF file paths to convert
            output_path: Output directory for Word files
            parameters: Conversion parameters (format, quality, etc.)
            
        Returns:
            List containing paths to converted Word files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for conversion")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        preserve_formatting = params.get("preserve_formatting", True)
        extract_images = params.get("extract_images", True)
        quality = params.get("quality", "high")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF to Word conversion",
                input_count=len(input_files),
                preserve_formatting=preserve_formatting,
                extract_images=extract_images,
                quality=quality
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Converting file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}.docx"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Convert the PDF
                    await self._convert_pdf_to_word(
                        input_file, 
                        output_file, 
                        preserve_formatting,
                        extract_images,
                        quality
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create Word document")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} converted successfully",
                        input_file=input_file,
                        output_file=output_file,
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to convert file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to convert {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PDF to Word conversion completed successfully",
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF to Word conversion: {str(e)}")
    
    async def _convert_pdf_to_word(
        self, 
        input_file: str, 
        output_file: str, 
        preserve_formatting: bool,
        extract_images: bool,
        quality: str
    ):
        """Convert a single PDF file to Word document using PyMuPDF."""
        try:
            # Open the PDF document
            doc = fitz.open(input_file)
            
            # Check if document is encrypted
            if doc.needs_pass:
                doc.close()
                raise ProcessingError("Cannot convert password-protected PDF")
            
            # Create a new Word document
            word_doc = Document()
            
            # Set quality parameters
            image_dpi = self._get_image_dpi(quality)
            
            page_count = len(doc)
            
            for page_num in range(page_count):
                page = doc[page_num]
                
                self.logger.debug(f"Processing page {page_num + 1}/{page_count}")
                
                # Add page break (except for first page)
                if page_num > 0:
                    word_doc.add_page_break()
                
                # Extract text content
                if preserve_formatting:
                    # Try to preserve formatting by extracting text blocks
                    text_dict = page.get_text("dict")
                    await self._process_text_blocks(word_doc, text_dict)
                else:
                    # Simple text extraction
                    text = page.get_text()
                    if text.strip():
                        word_doc.add_paragraph(text)
                
                # Extract images if requested
                if extract_images:
                    await self._extract_page_images(word_doc, page, image_dpi)
            
            # Save the Word document
            word_doc.save(output_file)
            
            # Close the PDF document
            doc.close()
            
        except Exception as e:
            raise ProcessingError(f"Failed to convert PDF to Word: {str(e)}")
    
    async def _process_text_blocks(self, word_doc: Document, text_dict: dict):
        """Process text blocks to preserve formatting."""
        try:
            if not text_dict or "blocks" not in text_dict:
                return
            
            for block in text_dict["blocks"]:
                if "lines" not in block:
                    continue
                
                # Process each line in the block
                block_text = ""
                for line in block["lines"]:
                    if "spans" not in line:
                        continue
                    
                    line_text = ""
                    for span in line["spans"]:
                        if "text" in span:
                            line_text += span["text"]
                    
                    if line_text.strip():
                        block_text += line_text + "\n"
                
                # Add the block as a paragraph
                if block_text.strip():
                    word_doc.add_paragraph(block_text.strip())
        
        except Exception as e:
            self.logger.warning("Failed to preserve formatting, using simple text extraction", error=str(e))
            # Fallback to simple text extraction
            text = ""
            if "blocks" in text_dict:
                for block in text_dict["blocks"]:
                    if "lines" in block:
                        for line in block["lines"]:
                            if "spans" in line:
                                for span in line["spans"]:
                                    if "text" in span:
                                        text += span["text"]
                        text += "\n"
            
            if text.strip():
                word_doc.add_paragraph(text.strip())
    
    async def _extract_page_images(self, word_doc: Document, page, image_dpi: int):
        """Extract images from a PDF page and add them to Word document."""
        try:
            # Get list of images on the page
            image_list = page.get_images()
            
            for img_index, img in enumerate(image_list):
                try:
                    # Get the image
                    xref = img[0]
                    pix = fitz.Pixmap(page.parent, xref)
                    
                    # Skip if image is too small or has unusual properties
                    if pix.width < 50 or pix.height < 50:
                        pix = None
                        continue
                    
                    # Convert to RGB if necessary
                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        img_data = pix.tobytes("png")
                    else:  # CMYK: convert to RGB first
                        pix1 = fitz.Pixmap(fitz.csRGB, pix)
                        img_data = pix1.tobytes("png")
                        pix1 = None
                    
                    # Save image temporarily
                    temp_img_path = f"temp_img_{img_index}.png"
                    with open(temp_img_path, "wb") as img_file:
                        img_file.write(img_data)
                    
                    # Add image to Word document
                    try:
                        # Calculate appropriate size (max 6 inches width)
                        max_width = Inches(6)
                        aspect_ratio = pix.height / pix.width
                        
                        if pix.width > 600:  # If image is large
                            width = max_width
                            height = width * aspect_ratio
                        else:
                            width = Inches(pix.width / image_dpi)
                            height = Inches(pix.height / image_dpi)
                        
                        word_doc.add_picture(temp_img_path, width=width, height=height)
                        word_doc.add_paragraph()  # Add space after image
                    
                    except Exception as e:
                        self.logger.warning(f"Failed to add image {img_index} to document", error=str(e))
                    
                    finally:
                        # Clean up temporary image file
                        if os.path.exists(temp_img_path):
                            os.remove(temp_img_path)
                    
                    pix = None
                
                except Exception as e:
                    self.logger.warning(f"Failed to extract image {img_index}", error=str(e))
                    continue
        
        except Exception as e:
            self.logger.warning("Failed to extract images from page", error=str(e))
    
    def _get_image_dpi(self, quality: str) -> int:
        """Get image DPI based on quality setting."""
        quality_settings = {
            "low": 72,
            "medium": 150,
            "high": 300
        }
        return quality_settings.get(quality, 150)
    
    def get_conversion_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available conversion options."""
        return {
            "preserve_formatting": {
                "description": "Attempt to preserve text formatting",
                "type": "boolean",
                "default": True
            },
            "extract_images": {
                "description": "Extract and include images",
                "type": "boolean", 
                "default": True
            },
            "quality": {
                "description": "Image quality for extracted images",
                "type": "string",
                "options": ["low", "medium", "high"],
                "default": "high"
            }
        }


# Create tool instance
pdf_to_word_tool = PDFToWordTool()
