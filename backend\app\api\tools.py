"""
PDF tools API endpoints.
"""
import os
import uuid
import json
import asyncio
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Request
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
import structlog

from ..config import settings
from ..database import get_db, User, ProcessingTask, UsageLog
from ..api.auth import get_current_user_optional
from ..schemas.tools import (
    ProcessingResponse, TaskStatus, SessionInfo, FileInfo,
    MergeRequest, CompressRequest, SplitRequest, ConversionRequest,
    WatermarkRequest, RotateRequest, ProtectRequest, UnlockRequest,
    OCRRequest, PageNumbersRequest
)
from ..core.session_manager import SessionManager
from ..core.file_manager import FileManager

logger = structlog.get_logger()
router = APIRouter()


def get_session_manager(request: Request) -> SessionManager:
    """Get session manager from app state."""
    return request.app.state.session_manager


def get_file_manager(request: Request) -> FileManager:
    """Get file manager from app state."""
    return request.app.state.file_manager


async def check_usage_limits(user: Optional[User], db: Session):
    """Check if user has exceeded usage limits."""
    if not user:
        # Anonymous users get limited access
        return True
    
    if user.plan == "premium":
        return True
    
    # Free users get 3 operations per day
    today = datetime.now().strftime("%Y-%m-%d")
    if user.last_usage_date != today:
        user.daily_usage = 0
        user.last_usage_date = today
        db.commit()
    
    if user.daily_usage >= 3:
        raise HTTPException(
            status_code=429,
            detail="Daily usage limit exceeded. Please upgrade to premium for unlimited access."
        )
    
    return True


async def increment_usage(user: Optional[User], db: Session):
    """Increment user's daily usage."""
    if user and user.plan != "premium":
        user.daily_usage += 1
        db.commit()


async def create_processing_task(
    session_id: str,
    user_id: Optional[int],
    tool_name: str,
    input_files: List[str],
    parameters: Optional[dict],
    db: Session
) -> str:
    """Create a new processing task."""
    task_id = str(uuid.uuid4())
    
    task = ProcessingTask(
        task_id=task_id,
        session_id=session_id,
        user_id=user_id,
        tool_name=tool_name,
        input_files=json.dumps(input_files),
        parameters=json.dumps(parameters) if parameters else None,
        status="pending"
    )
    
    db.add(task)
    db.commit()
    
    return task_id


@router.post("/session", response_model=SessionInfo)
async def create_session(
    request: Request,
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Create a new processing session."""
    session_manager = get_session_manager(request)
    session_id = await session_manager.create_session(user.id if user else None)
    
    session = await session_manager.get_session(session_id)
    
    return SessionInfo(
        session_id=session_id,
        created_at=session["created_at"],
        last_activity=session["last_activity"],
        active_tasks=len(session["active_tasks"])
    )


@router.get("/session/{session_id}", response_model=SessionInfo)
async def get_session_info(
    session_id: str,
    request: Request
):
    """Get session information."""
    session_manager = get_session_manager(request)
    session = await session_manager.get_session(session_id)
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return SessionInfo(
        session_id=session_id,
        created_at=session["created_at"],
        last_activity=session["last_activity"],
        active_tasks=len(session["active_tasks"])
    )


@router.get("/task/{task_id}", response_model=TaskStatus)
async def get_task_status(task_id: str, db: Session = Depends(get_db)):
    """Get task status."""
    task = db.query(ProcessingTask).filter(ProcessingTask.task_id == task_id).first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # Prepare message based on status
    if task.status == "failed":
        message = task.error_message or "Processing failed"
    elif task.status == "completed":
        message = "Processing completed successfully"
    else:
        message = "Processing..."

    # Parse output files if available
    output_files = None
    if task.output_files:
        try:
            output_files = json.loads(task.output_files)
        except (json.JSONDecodeError, TypeError):
            output_files = None

    # Calculate processing time if completed
    processing_time = None
    if task.completed_at and task.started_at:
        processing_time = (task.completed_at - task.started_at).total_seconds()

    return TaskStatus(
        task_id=task.task_id,
        status=task.status,
        message=message,
        error=task.error_message if task.status == "failed" else None,
        output_files=output_files,
        error_message=task.error_message,
        processing_time=processing_time,
        created_at=task.created_at,
        started_at=task.started_at,
        completed_at=task.completed_at
    )


@router.get("/download/{task_id}")
async def download_result(
    task_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """Download processed file."""
    task = db.query(ProcessingTask).filter(ProcessingTask.task_id == task_id).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    if task.status != "completed":
        raise HTTPException(status_code=400, detail="Task not completed")
    
    if not task.output_files:
        raise HTTPException(status_code=404, detail="No output files available")
    
    output_files = json.loads(task.output_files)
    if not output_files:
        raise HTTPException(status_code=404, detail="No output files available")
    
    # Return the first output file
    file_path = output_files[0]
    file_manager = get_file_manager(request)
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Output file not found")
    
    # Generate appropriate filename
    original_filename = "processed_file.pdf"  # Default
    if task.input_files:
        input_files = json.loads(task.input_files)
        if input_files:
            original_filename = os.path.basename(input_files[0])
    
    download_filename = file_manager.get_download_filename(original_filename, task.tool_name)
    
    return FileResponse(
        path=file_path,
        filename=download_filename,
        media_type='application/octet-stream'
    )


# Import tools
from ..tools.merge_pdf import merge_tool
from ..tools.compress_pdf import compress_tool
from ..tools.split_pdf import split_tool
from ..tools.pdf_to_word import pdf_to_word_tool
from ..tools.pdf_to_image import pdf_to_image_tool
from ..tools.image_to_pdf import image_to_pdf_tool
from ..tools.protect_pdf import protect_pdf_tool
from ..tools.unlock_pdf import unlock_pdf_tool
from ..tools.watermark_pdf import watermark_pdf_tool
from ..tools.rotate_pdf import rotate_pdf_tool
from ..tools.ocr_pdf import ocr_pdf_tool
from ..tools.repair_pdf import repair_pdf_tool
from ..tools.organize_pdf import organize_pdf_tool
from ..tools.crop_pdf import crop_pdf_tool
from ..tools.page_numbers_pdf import page_numbers_pdf_tool
from ..tools.redact_pdf import redact_pdf_tool
from ..tools.word_to_pdf import word_to_pdf_tool
from ..tools.excel_to_pdf import excel_to_pdf_tool
from ..tools.html_to_pdf import html_to_pdf_tool
from ..tools.compare_pdf import compare_pdf_tool
from ..tools.sign_pdf import sign_pdf_tool
from ..tools.pdf_to_excel import pdf_to_excel_tool
from ..tools.powerpoint_to_pdf import powerpoint_to_pdf_tool
from ..tools.pdf_to_powerpoint import pdf_to_powerpoint_tool
from ..tools.pdf_to_pdfa import pdf_to_pdfa_tool
from ..tools.edit_pdf import edit_pdf_tool
from ..tools.scan_to_pdf import scan_to_pdf_tool

# Tool-specific endpoints

@router.post("/merge", response_model=ProcessingResponse)
async def merge_pdfs(
    request: Request,
    session_id: str = Form(...),
    file_order: Optional[str] = Form(None),
    preserve_bookmarks: bool = Form(True),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Merge multiple PDF files into one."""
    if len(files) < 2:
        raise HTTPException(status_code=400, detail="At least 2 PDF files are required")

    session_manager = get_session_manager(request)
    file_manager = get_file_manager(request)

    # Verify session exists
    session_dir = await session_manager.get_session_dir(session_id)
    if not session_dir:
        raise HTTPException(status_code=404, detail="Session not found")

    # Check usage limits
    await check_usage_limits(user, db)

    try:
        # Save uploaded files
        input_files = await file_manager.save_multiple_files(files, session_dir)

        # Parse file order if provided
        parameters = {"preserve_bookmarks": preserve_bookmarks}
        if file_order:
            try:
                order_list = [int(x.strip()) for x in file_order.split(",")]
                parameters["file_order"] = order_list
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid file_order format")

        # Create processing task
        task_id = await create_processing_task(
            session_id=session_id,
            user_id=user.id if user else None,
            tool_name="merge",
            input_files=input_files,
            parameters=parameters,
            db=db
        )

        # Add task to session
        await session_manager.add_task_to_session(session_id, task_id)

        # Process files asynchronously
        asyncio.create_task(process_merge_task(task_id, input_files, session_dir, parameters, db, session_manager))

        # Increment usage
        await increment_usage(user, db)

        return ProcessingResponse(
            task_id=task_id,
            status="processing",
            message="PDF merge started",
            session_id=session_id,
            created_at=datetime.now()
        )

    except Exception as e:
        logger.error("Failed to start merge process", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to start merge process")


async def process_merge_task(
    task_id: str,
    input_files: List[str],
    session_dir: str,
    parameters: dict,
    db: Session,
    session_manager: SessionManager
):
    """Process merge task asynchronously."""
    try:
        # Update task status
        task = db.query(ProcessingTask).filter(ProcessingTask.task_id == task_id).first()
        if task:
            task.status = "processing"
            task.started_at = datetime.now()
            db.commit()

        # Create output path
        output_dir = os.path.join(session_dir, "outputs")

        # Process with merge tool
        output_files = await merge_tool.process_with_timeout(
            input_files=input_files,
            output_path=output_dir,
            parameters=parameters,
            timeout_seconds=300
        )

        # Update task with success
        if task:
            task.status = "completed"
            task.output_files = json.dumps(output_files)
            task.completed_at = datetime.now()
            processing_time = (task.completed_at - task.started_at).total_seconds()
            task.processing_time = processing_time
            db.commit()

        logger.info("Merge task completed successfully", task_id=task_id)

    except Exception as e:
        # Update task with error
        if task:
            task.status = "failed"
            task.error_message = str(e)
            task.completed_at = datetime.now()
            db.commit()

        logger.error("Merge task failed", task_id=task_id, error=str(e))

    finally:
        # Remove task from session
        await session_manager.remove_task_from_session(task.session_id if task else "", task_id)


@router.post("/compress", response_model=ProcessingResponse)
async def compress_pdf(
    request: Request,
    session_id: str = Form(...),
    compression_level: str = Form("medium"),
    image_quality: int = Form(85),
    remove_metadata: bool = Form(True),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Compress PDF files to reduce file size."""
    if len(files) == 0:
        raise HTTPException(status_code=400, detail="At least 1 PDF file is required")

    # Validate compression level
    if compression_level not in compress_tool.get_available_levels():
        raise HTTPException(
            status_code=400,
            detail=f"Invalid compression level. Available: {compress_tool.get_available_levels()}"
        )

    session_manager = get_session_manager(request)
    file_manager = get_file_manager(request)

    # Verify session exists
    session_dir = await session_manager.get_session_dir(session_id)
    if not session_dir:
        raise HTTPException(status_code=404, detail="Session not found")

    # Check usage limits
    await check_usage_limits(user, db)

    try:
        # Save uploaded files
        input_files = await file_manager.save_multiple_files(files, session_dir)

        # Prepare parameters
        parameters = {
            "compression_level": compression_level,
            "image_quality": image_quality,
            "remove_metadata": remove_metadata
        }

        # Create processing task
        task_id = await create_processing_task(
            session_id=session_id,
            user_id=user.id if user else None,
            tool_name="compress",
            input_files=input_files,
            parameters=parameters,
            db=db
        )

        # Add task to session
        await session_manager.add_task_to_session(session_id, task_id)

        # Process files asynchronously
        asyncio.create_task(process_compress_task(task_id, input_files, session_dir, parameters, db, session_manager))

        # Increment usage
        await increment_usage(user, db)

        return ProcessingResponse(
            task_id=task_id,
            status="processing",
            message=f"PDF compression started with {compression_level} level",
            session_id=session_id,
            created_at=datetime.now()
        )

    except Exception as e:
        logger.error("Failed to start compress process", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to start compress process")


async def process_compress_task(
    task_id: str,
    input_files: List[str],
    session_dir: str,
    parameters: dict,
    db: Session,
    session_manager: SessionManager
):
    """Process compress task asynchronously."""
    try:
        # Update task status
        task = db.query(ProcessingTask).filter(ProcessingTask.task_id == task_id).first()
        if task:
            task.status = "processing"
            task.started_at = datetime.now()
            db.commit()

        # Create output path
        output_dir = os.path.join(session_dir, "outputs")

        # Process with compress tool
        output_files = await compress_tool.process_with_timeout(
            input_files=input_files,
            output_path=output_dir,
            parameters=parameters,
            timeout_seconds=300
        )

        # Update task with success
        if task:
            task.status = "completed"
            task.output_files = json.dumps(output_files)
            task.completed_at = datetime.now()
            processing_time = (task.completed_at - task.started_at).total_seconds()
            task.processing_time = processing_time
            db.commit()

        logger.info("Compress task completed successfully", task_id=task_id)

    except Exception as e:
        # Update task with error
        if task:
            task.status = "failed"
            task.error_message = str(e)
            task.completed_at = datetime.now()
            db.commit()

        logger.error("Compress task failed", task_id=task_id, error=str(e))

    finally:
        # Remove task from session
        await session_manager.remove_task_from_session(task.session_id if task else "", task_id)


@router.get("/compress/levels")
async def get_compression_levels():
    """Get available compression levels and their descriptions."""
    levels = {}
    for level in compress_tool.get_available_levels():
        levels[level] = compress_tool.get_compression_info(level)
    return levels


@router.post("/split", response_model=ProcessingResponse)
async def split_pdf(
    request: Request,
    session_id: str = Form(...),
    split_type: str = Form("pages"),
    split_value: str = Form("1"),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Split PDF file into multiple parts."""
    if len(files) != 1:
        raise HTTPException(status_code=400, detail="PDF split requires exactly 1 file")

    # Validate split type
    valid_types = list(split_tool.get_split_options().keys())
    if split_type not in valid_types:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid split type. Available: {valid_types}"
        )

    session_manager = get_session_manager(request)
    file_manager = get_file_manager(request)

    # Verify session exists
    session_dir = await session_manager.get_session_dir(session_id)
    if not session_dir:
        raise HTTPException(status_code=404, detail="Session not found")

    # Check usage limits
    await check_usage_limits(user, db)

    try:
        # Save uploaded file
        input_files = await file_manager.save_multiple_files(files, session_dir)

        # Prepare parameters
        parameters = {
            "split_type": split_type,
            "split_value": split_value
        }

        # Create processing task
        task_id = await create_processing_task(
            session_id=session_id,
            user_id=user.id if user else None,
            tool_name="split",
            input_files=input_files,
            parameters=parameters,
            db=db
        )

        # Add task to session
        await session_manager.add_task_to_session(session_id, task_id)

        # Process files asynchronously
        asyncio.create_task(process_split_task(task_id, input_files, session_dir, parameters, db, session_manager))

        # Increment usage
        await increment_usage(user, db)

        return ProcessingResponse(
            task_id=task_id,
            status="processing",
            message=f"PDF split started with {split_type} method",
            session_id=session_id,
            created_at=datetime.now()
        )

    except Exception as e:
        logger.error("Failed to start split process", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to start split process")


async def process_split_task(
    task_id: str,
    input_files: List[str],
    session_dir: str,
    parameters: dict,
    db: Session,
    session_manager: SessionManager
):
    """Process split task asynchronously."""
    try:
        # Update task status
        task = db.query(ProcessingTask).filter(ProcessingTask.task_id == task_id).first()
        if task:
            task.status = "processing"
            task.started_at = datetime.now()
            db.commit()

        # Create output path
        output_dir = os.path.join(session_dir, "outputs")

        # Process with split tool
        output_files = await split_tool.process_with_timeout(
            input_files=input_files,
            output_path=output_dir,
            parameters=parameters,
            timeout_seconds=300
        )

        # Update task with success
        if task:
            task.status = "completed"
            task.output_files = json.dumps(output_files)
            task.completed_at = datetime.now()
            processing_time = (task.completed_at - task.started_at).total_seconds()
            task.processing_time = processing_time
            db.commit()

        logger.info("Split task completed successfully", task_id=task_id)

    except Exception as e:
        # Update task with error
        if task:
            task.status = "failed"
            task.error_message = str(e)
            task.completed_at = datetime.now()
            db.commit()

        logger.error("Split task failed", task_id=task_id, error=str(e))

    finally:
        # Remove task from session
        await session_manager.remove_task_from_session(task.session_id if task else "", task_id)


@router.get("/split/options")
async def get_split_options():
    """Get available split options and their descriptions."""
    return split_tool.get_split_options()


async def process_conversion_task(
    request: Request,
    session_id: str,
    tool_name: str,
    files: List[UploadFile],
    parameters: dict,
    tool_instance,
    db: Session,
    user: Optional[User]
) -> ProcessingResponse:
    """Generic function to process conversion tasks."""
    if len(files) == 0:
        raise HTTPException(status_code=400, detail="At least 1 file is required")

    session_manager = get_session_manager(request)
    file_manager = get_file_manager(request)

    # Verify session exists
    session_dir = await session_manager.get_session_dir(session_id)
    if not session_dir:
        raise HTTPException(status_code=404, detail="Session not found")

    # Check usage limits
    await check_usage_limits(user, db)

    try:
        # Save uploaded files
        input_files = await file_manager.save_multiple_files(files, session_dir)

        # Create processing task
        task_id = await create_processing_task(
            session_id=session_id,
            user_id=user.id if user else None,
            tool_name=tool_name,
            input_files=input_files,
            parameters=parameters,
            db=db
        )

        # Add task to session
        await session_manager.add_task_to_session(session_id, task_id)

        # Process files asynchronously
        asyncio.create_task(
            process_generic_task(task_id, input_files, session_dir, parameters, tool_instance, db, session_manager)
        )

        # Increment usage
        await increment_usage(user, db)

        return ProcessingResponse(
            task_id=task_id,
            status="processing",
            message=f"{tool_name} processing started",
            session_id=session_id,
            created_at=datetime.now()
        )

    except Exception as e:
        logger.error(f"Failed to start {tool_name} process", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to start {tool_name} process")


async def process_generic_task(
    task_id: str,
    input_files: List[str],
    session_dir: str,
    parameters: dict,
    tool_instance,
    db: Session,
    session_manager: SessionManager
):
    """Process generic task asynchronously."""
    try:
        # Update task status
        task = db.query(ProcessingTask).filter(ProcessingTask.task_id == task_id).first()
        if task:
            task.status = "processing"
            task.started_at = datetime.now()
            db.commit()

        # Create output path
        output_dir = os.path.join(session_dir, "outputs")

        # Process with tool
        output_files = await tool_instance.process_with_timeout(
            input_files=input_files,
            output_path=output_dir,
            parameters=parameters,
            timeout_seconds=300
        )

        # Update task with success
        if task:
            task.status = "completed"
            task.output_files = json.dumps(output_files)
            task.completed_at = datetime.now()
            processing_time = (task.completed_at - task.started_at).total_seconds()
            task.processing_time = processing_time
            db.commit()

        logger.info("Task completed successfully", task_id=task_id)

    except Exception as e:
        # Update task with error
        if task:
            task.status = "failed"
            task.error_message = str(e)
            task.completed_at = datetime.now()
            db.commit()

        logger.error("Task failed", task_id=task_id, error=str(e))

    finally:
        # Remove task from session
        await session_manager.remove_task_from_session(task.session_id if task else "", task_id)


@router.post("/upload")
async def upload_files(
    request: Request,
    session_id: str = Form(...),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Upload files to a session."""
    session_manager = get_session_manager(request)
    file_manager = get_file_manager(request)
    
    # Verify session exists
    session_dir = await session_manager.get_session_dir(session_id)
    if not session_dir:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Check usage limits
    await check_usage_limits(user, db)
    
    # Save uploaded files
    try:
        saved_files = await file_manager.save_multiple_files(files, session_dir)
        
        # Get file info
        file_infos = []
        for file_path in saved_files:
            info = file_manager.get_file_info(file_path)
            file_infos.append(FileInfo(
                filename=info["filename"],
                size=info["size"],
                mime_type=info["mime_type"],
                extension=info["extension"]
            ))
        
        logger.info(
            "Files uploaded successfully",
            session_id=session_id,
            user_id=user.id if user else None,
            file_count=len(saved_files)
        )
        
        return {
            "message": "Files uploaded successfully",
            "session_id": session_id,
            "files": file_infos,
            "file_paths": saved_files  # Internal use
        }
        
    except Exception as e:
        logger.error("Failed to upload files", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to upload files")


# PDF to Word Conversion
@router.post("/convert/pdf-to-word", response_model=ProcessingResponse)
async def convert_pdf_to_word(
    request: Request,
    session_id: str = Form(...),
    extract_images: bool = Form(True),
    preserve_layout: bool = Form(True),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Convert PDF files to Word documents."""
    return await process_conversion_task(
        request, session_id, "pdf_to_word", files,
        {"extract_images": extract_images, "preserve_layout": preserve_layout},
        pdf_to_word_tool, db, user
    )


# PDF to Image Conversion
@router.post("/convert/pdf-to-image", response_model=ProcessingResponse)
async def convert_pdf_to_image(
    request: Request,
    session_id: str = Form(...),
    output_format: str = Form("jpg"),
    dpi: int = Form(300),
    pages: str = Form("all"),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Convert PDF files to images."""
    return await process_conversion_task(
        request, session_id, "pdf_to_image", files,
        {"output_format": output_format, "dpi": dpi, "pages": pages},
        pdf_to_image_tool, db, user
    )


# Image to PDF Conversion
@router.post("/convert/image-to-pdf", response_model=ProcessingResponse)
async def convert_image_to_pdf(
    request: Request,
    session_id: str = Form(...),
    page_size: str = Form("A4"),
    orientation: str = Form("auto"),
    margin: float = Form(1.0),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Convert images to PDF."""
    return await process_conversion_task(
        request, session_id, "image_to_pdf", files,
        {"page_size": page_size, "orientation": orientation, "margin": margin},
        image_to_pdf_tool, db, user
    )


# PDF Protection
@router.post("/protect", response_model=ProcessingResponse)
async def protect_pdf(
    request: Request,
    session_id: str = Form(...),
    user_password: str = Form(...),
    owner_password: Optional[str] = Form(None),
    allow_printing: bool = Form(True),
    allow_copying: bool = Form(False),
    allow_modification: bool = Form(False),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Add password protection to PDF files."""
    return await process_conversion_task(
        request, session_id, "protect", files,
        {
            "user_password": user_password,
            "owner_password": owner_password,
            "allow_printing": allow_printing,
            "allow_copying": allow_copying,
            "allow_modification": allow_modification
        },
        protect_pdf_tool, db, user
    )


# PDF Unlock
@router.post("/unlock", response_model=ProcessingResponse)
async def unlock_pdf(
    request: Request,
    session_id: str = Form(...),
    password: str = Form(...),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Remove password protection from PDF files."""
    return await process_conversion_task(
        request, session_id, "unlock", files,
        {"password": password},
        unlock_pdf_tool, db, user
    )


# PDF Watermark
@router.post("/watermark", response_model=ProcessingResponse)
async def add_watermark(
    request: Request,
    session_id: str = Form(...),
    watermark_text: str = Form(...),
    position: str = Form("center"),
    opacity: float = Form(0.5),
    font_size: int = Form(50),
    color: str = Form("gray"),
    pages: str = Form("all"),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Add watermark to PDF files."""
    return await process_conversion_task(
        request, session_id, "watermark", files,
        {
            "watermark_text": watermark_text,
            "position": position,
            "opacity": opacity,
            "font_size": font_size,
            "color": color,
            "pages": pages
        },
        watermark_pdf_tool, db, user
    )


# PDF Rotation
@router.post("/rotate", response_model=ProcessingResponse)
async def rotate_pdf(
    request: Request,
    session_id: str = Form(...),
    rotation: int = Form(90),
    pages: str = Form("all"),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Rotate PDF pages."""
    return await process_conversion_task(
        request, session_id, "rotate", files,
        {"rotation": rotation, "pages": pages},
        rotate_pdf_tool, db, user
    )


# PDF OCR
@router.post("/ocr", response_model=ProcessingResponse)
async def ocr_pdf(
    request: Request,
    session_id: str = Form(...),
    language: str = Form("eng"),
    output_format: str = Form("pdf"),
    dpi: int = Form(300),
    preprocessing: bool = Form(True),
    confidence_threshold: int = Form(60),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Perform OCR on PDF files."""
    return await process_conversion_task(
        request, session_id, "ocr", files,
        {
            "language": language,
            "output_format": output_format,
            "dpi": dpi,
            "preprocessing": preprocessing,
            "confidence_threshold": confidence_threshold
        },
        ocr_pdf_tool, db, user
    )


# PDF Repair
@router.post("/repair", response_model=ProcessingResponse)
async def repair_pdf(
    request: Request,
    session_id: str = Form(...),
    repair_strategy: str = Form("basic"),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Repair corrupted PDF files."""
    return await process_conversion_task(
        request, session_id, "repair", files,
        {"repair_strategy": repair_strategy},
        repair_pdf_tool, db, user
    )


# PDF Organize
@router.post("/organize", response_model=ProcessingResponse)
async def organize_pdf(
    request: Request,
    session_id: str = Form(...),
    operation: str = Form("reorder"),
    page_order: Optional[str] = Form(None),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Organize PDF pages."""
    return await process_conversion_task(
        request, session_id, "organize", files,
        {"operation": operation, "page_order": page_order},
        organize_pdf_tool, db, user
    )


# PDF Crop
@router.post("/crop", response_model=ProcessingResponse)
async def crop_pdf(
    request: Request,
    session_id: str = Form(...),
    crop_mode: str = Form("margins"),
    margin_threshold: float = Form(10),
    pages: str = Form("all"),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Crop PDF pages."""
    return await process_conversion_task(
        request, session_id, "crop", files,
        {
            "crop_mode": crop_mode,
            "margin_threshold": margin_threshold,
            "pages": pages
        },
        crop_pdf_tool, db, user
    )


# PDF Page Numbers
@router.post("/page-numbers", response_model=ProcessingResponse)
async def add_page_numbers(
    request: Request,
    session_id: str = Form(...),
    position: str = Form("bottom-center"),
    format_style: str = Form("number"),
    start_number: int = Form(1),
    font_size: int = Form(12),
    pages: str = Form("all"),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Add page numbers to PDF files."""
    return await process_conversion_task(
        request, session_id, "page_numbers", files,
        {
            "position": position,
            "format_style": format_style,
            "start_number": start_number,
            "font_size": font_size,
            "pages": pages
        },
        page_numbers_pdf_tool, db, user
    )


# PDF Redaction
@router.post("/redact", response_model=ProcessingResponse)
async def redact_pdf(
    request: Request,
    session_id: str = Form(...),
    patterns: str = Form("ssn,phone,email"),
    method: str = Form("black_box"),
    pages: str = Form("all"),
    case_sensitive: bool = Form(False),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Redact sensitive information from PDF files."""
    pattern_list = [p.strip() for p in patterns.split(",") if p.strip()]
    return await process_conversion_task(
        request, session_id, "redact", files,
        {
            "patterns": pattern_list,
            "method": method,
            "pages": pages,
            "case_sensitive": case_sensitive
        },
        redact_pdf_tool, db, user
    )


# Word to PDF Conversion
@router.post("/convert/word-to-pdf", response_model=ProcessingResponse)
async def convert_word_to_pdf(
    request: Request,
    session_id: str = Form(...),
    quality: str = Form("high"),
    preserve_layout: bool = Form(True),
    include_metadata: bool = Form(True),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Convert Word documents to PDF."""
    return await process_conversion_task(
        request, session_id, "word_to_pdf", files,
        {
            "quality": quality,
            "preserve_layout": preserve_layout,
            "include_metadata": include_metadata
        },
        word_to_pdf_tool, db, user
    )


# Excel to PDF Conversion
@router.post("/convert/excel-to-pdf", response_model=ProcessingResponse)
async def convert_excel_to_pdf(
    request: Request,
    session_id: str = Form(...),
    orientation: str = Form("portrait"),
    sheets: str = Form("all"),
    fit_to_page: bool = Form(True),
    include_gridlines: bool = Form(True),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Convert Excel spreadsheets to PDF."""
    return await process_conversion_task(
        request, session_id, "excel_to_pdf", files,
        {
            "orientation": orientation,
            "sheets": sheets,
            "fit_to_page": fit_to_page,
            "include_gridlines": include_gridlines
        },
        excel_to_pdf_tool, db, user
    )


# HTML to PDF Conversion
@router.post("/convert/html-to-pdf", response_model=ProcessingResponse)
async def convert_html_to_pdf(
    request: Request,
    session_id: str = Form(...),
    page_size: str = Form("A4"),
    orientation: str = Form("portrait"),
    margin_top: str = Form("1cm"),
    margin_bottom: str = Form("1cm"),
    margin_left: str = Form("1cm"),
    margin_right: str = Form("1cm"),
    include_background: bool = Form(True),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Convert HTML files to PDF."""
    return await process_conversion_task(
        request, session_id, "html_to_pdf", files,
        {
            "page_size": page_size,
            "orientation": orientation,
            "margin_top": margin_top,
            "margin_bottom": margin_bottom,
            "margin_left": margin_left,
            "margin_right": margin_right,
            "include_background": include_background
        },
        html_to_pdf_tool, db, user
    )


# PDF Comparison
@router.post("/compare", response_model=ProcessingResponse)
async def compare_pdf(
    request: Request,
    session_id: str = Form(...),
    method: str = Form("both"),
    sensitivity: str = Form("medium"),
    highlight_color: str = Form("red"),
    generate_report: bool = Form(True),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Compare two PDF files."""
    if len(files) != 2:
        raise HTTPException(status_code=400, detail="Exactly 2 files are required for comparison")

    return await process_conversion_task(
        request, session_id, "compare", files,
        {
            "method": method,
            "sensitivity": sensitivity,
            "highlight_color": highlight_color,
            "generate_report": generate_report
        },
        compare_pdf_tool, db, user
    )


# PDF Digital Signature
@router.post("/sign", response_model=ProcessingResponse)
async def sign_pdf(
    request: Request,
    session_id: str = Form(...),
    signature_type: str = Form("text"),
    signature_text: str = Form("Digitally Signed"),
    position: str = Form("bottom-right"),
    pages: str = Form("last"),
    signer_name: str = Form(""),
    reason: str = Form("Document approval"),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Add digital signatures to PDF files."""
    return await process_conversion_task(
        request, session_id, "sign", files,
        {
            "signature_type": signature_type,
            "signature_text": signature_text,
            "position": position,
            "pages": pages,
            "signer_name": signer_name,
            "reason": reason
        },
        sign_pdf_tool, db, user
    )


# PDF to Excel Conversion
@router.post("/convert/pdf-to-excel", response_model=ProcessingResponse)
async def convert_pdf_to_excel(
    request: Request,
    session_id: str = Form(...),
    output_format: str = Form("xlsx"),
    extraction_method: str = Form("auto"),
    pages: str = Form("all"),
    include_headers: bool = Form(True),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Convert PDF files to Excel format."""
    return await process_conversion_task(
        request, session_id, "pdf_to_excel", files,
        {
            "output_format": output_format,
            "extraction_method": extraction_method,
            "pages": pages,
            "include_headers": include_headers
        },
        pdf_to_excel_tool, db, user
    )


# PowerPoint to PDF Conversion
@router.post("/convert/powerpoint-to-pdf", response_model=ProcessingResponse)
async def convert_powerpoint_to_pdf(
    request: Request,
    session_id: str = Form(...),
    quality: str = Form("high"),
    include_notes: bool = Form(False),
    slides_per_page: int = Form(1),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Convert PowerPoint presentations to PDF."""
    return await process_conversion_task(
        request, session_id, "powerpoint_to_pdf", files,
        {
            "quality": quality,
            "include_notes": include_notes,
            "slides_per_page": slides_per_page
        },
        powerpoint_to_pdf_tool, db, user
    )


# PDF to PowerPoint Conversion
@router.post("/convert/pdf-to-powerpoint", response_model=ProcessingResponse)
async def convert_pdf_to_powerpoint(
    request: Request,
    session_id: str = Form(...),
    output_format: str = Form("pptx"),
    conversion_method: str = Form("image"),
    pages: str = Form("all"),
    slide_layout: str = Form("blank"),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Convert PDF files to PowerPoint presentations."""
    return await process_conversion_task(
        request, session_id, "pdf_to_powerpoint", files,
        {
            "output_format": output_format,
            "conversion_method": conversion_method,
            "pages": pages,
            "slide_layout": slide_layout
        },
        pdf_to_powerpoint_tool, db, user
    )


# PDF to PDF/A Conversion
@router.post("/convert/pdf-to-pdfa", response_model=ProcessingResponse)
async def convert_pdf_to_pdfa(
    request: Request,
    session_id: str = Form(...),
    conformance_level: str = Form("pdfa-2b"),
    color_profile: str = Form("srgb"),
    embed_fonts: bool = Form(True),
    optimize_images: bool = Form(True),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Convert PDF files to PDF/A archival format."""
    return await process_conversion_task(
        request, session_id, "pdf_to_pdfa", files,
        {
            "conformance_level": conformance_level,
            "color_profile": color_profile,
            "embed_fonts": embed_fonts,
            "optimize_images": optimize_images
        },
        pdf_to_pdfa_tool, db, user
    )


# PDF Edit
@router.post("/edit", response_model=ProcessingResponse)
async def edit_pdf(
    request: Request,
    session_id: str = Form(...),
    operations: str = Form(...),  # JSON string of operations
    pages: str = Form("all"),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Edit PDF files with specified operations."""
    try:
        import json
        operations_list = json.loads(operations)
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid operations JSON format")

    return await process_conversion_task(
        request, session_id, "edit", files,
        {
            "operations": operations_list,
            "pages": pages
        },
        edit_pdf_tool, db, user
    )


# Scan to PDF
@router.post("/scan-to-pdf", response_model=ProcessingResponse)
async def scan_to_pdf(
    request: Request,
    session_id: str = Form(...),
    ocr_enabled: bool = Form(True),
    ocr_language: str = Form("eng"),
    page_size: str = Form("auto"),
    combine_pages: bool = Form(True),
    dpi: int = Form(300),
    enhance_image: bool = Form(True),
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    user: Optional[User] = Depends(get_current_user_optional)
):
    """Convert scanned images to searchable PDF documents."""
    return await process_conversion_task(
        request, session_id, "scan_to_pdf", files,
        {
            "ocr_enabled": ocr_enabled,
            "ocr_language": ocr_language,
            "page_size": page_size,
            "combine_pages": combine_pages,
            "dpi": dpi,
            "enhance_image": enhance_image
        },
        scan_to_pdf_tool, db, user
    )


# Information endpoints for all tools
@router.get("/ocr/languages")
async def get_ocr_languages():
    """Get available OCR languages."""
    return ocr_pdf_tool.get_supported_languages()


@router.get("/repair/strategies")
async def get_repair_strategies():
    """Get available repair strategies."""
    return repair_pdf_tool.get_repair_strategies()


@router.get("/organize/operations")
async def get_organize_operations():
    """Get available organization operations."""
    return organize_pdf_tool.get_available_operations()


@router.get("/crop/modes")
async def get_crop_modes():
    """Get available crop modes."""
    return crop_pdf_tool.get_crop_modes()


@router.get("/page-numbers/formats")
async def get_page_number_formats():
    """Get available page number formats."""
    return page_numbers_pdf_tool.get_available_formats()


@router.get("/redact/patterns")
async def get_redaction_patterns():
    """Get available redaction patterns."""
    return redact_pdf_tool.get_redaction_patterns()


@router.get("/convert/word-to-pdf/support")
async def get_word_to_pdf_support():
    """Check Word to PDF conversion support."""
    return await word_to_pdf_tool.check_conversion_support()


@router.get("/convert/excel-to-pdf/options")
async def get_excel_to_pdf_options():
    """Get Excel to PDF conversion options."""
    return excel_to_pdf_tool.get_conversion_options()


@router.get("/convert/html-to-pdf/support")
async def get_html_to_pdf_support():
    """Check HTML to PDF conversion support."""
    return await html_to_pdf_tool.check_conversion_support()


@router.get("/compare/options")
async def get_compare_options():
    """Get PDF comparison options."""
    return compare_pdf_tool.get_comparison_options()


@router.get("/sign/options")
async def get_signature_options():
    """Get digital signature options."""
    return sign_pdf_tool.get_signature_options()


@router.get("/convert/pdf-to-excel/options")
async def get_pdf_to_excel_options():
    """Get PDF to Excel conversion options."""
    return pdf_to_excel_tool.get_conversion_options()


@router.get("/convert/powerpoint-to-pdf/support")
async def get_powerpoint_to_pdf_support():
    """Check PowerPoint to PDF conversion support."""
    return await powerpoint_to_pdf_tool.check_conversion_support()


@router.get("/convert/pdf-to-powerpoint/options")
async def get_pdf_to_powerpoint_options():
    """Get PDF to PowerPoint conversion options."""
    return pdf_to_powerpoint_tool.get_conversion_options()


@router.get("/convert/pdf-to-pdfa/options")
async def get_pdf_to_pdfa_options():
    """Get PDF to PDF/A conversion options."""
    return pdf_to_pdfa_tool.get_conversion_options()


@router.get("/edit/operations")
async def get_edit_operations():
    """Get available PDF edit operations."""
    return edit_pdf_tool.get_edit_operations()


@router.get("/scan-to-pdf/options")
async def get_scan_to_pdf_options():
    """Get scan to PDF conversion options."""
    return scan_to_pdf_tool.get_conversion_options()
