#!/usr/bin/env python3
"""
Dependency installation script for PDF Processing Platform
Handles installation of all required packages with proper error handling
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def install_core_dependencies():
    """Install core dependencies first."""
    print("📦 Installing core dependencies...")
    
    core_packages = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "PyJWT==2.8.0",
        "passlib[bcrypt]==1.7.4",
        "python-multipart==0.0.6",
        "sqlalchemy==2.0.23",
        "alembic==1.12.1",
        "psycopg2-binary==2.9.9",
        "PyMuPDF==1.23.8",
        "Pillow==10.1.0",
        "reportlab==4.0.7",
        "structlog==23.2.0"
    ]
    
    for package in core_packages:
        if not run_command(f"pip install {package}", f"Installing {package.split('==')[0]}"):
            return False
    
    return True

def install_optional_dependencies():
    """Install optional dependencies with fallbacks."""
    print("\n📦 Installing optional dependencies...")
    
    optional_packages = [
        ("pytesseract==0.3.10", "OCR support"),
        ("opencv-python==********", "Image processing"),
        ("openpyxl==3.1.2", "Excel support"),
        ("python-docx==1.1.0", "Word document support"),
        ("python-pptx==0.6.23", "PowerPoint support"),
        ("pdfplumber==0.10.3", "PDF text extraction"),
        ("img2pdf==0.5.1", "Image to PDF conversion"),
        ("chardet==5.2.0", "Character encoding detection")
    ]
    
    success_count = 0
    for package, description in optional_packages:
        if run_command(f"pip install {package}", f"Installing {description}"):
            success_count += 1
        else:
            print(f"⚠️  {description} will not be available")
    
    print(f"\n📊 Optional packages: {success_count}/{len(optional_packages)} installed successfully")
    return success_count > 0

def install_advanced_dependencies():
    """Install advanced dependencies that might require system libraries."""
    print("\n📦 Installing advanced dependencies...")
    
    advanced_packages = [
        ("tabula-py==2.9.0", "PDF table extraction", "Requires Java Runtime"),
        ("weasyprint==60.2", "HTML to PDF conversion", "Requires system libraries"),
        ("pdfkit==1.0.0", "HTML to PDF conversion", "Requires wkhtmltopdf"),
    ]
    
    success_count = 0
    for package, description, note in advanced_packages:
        print(f"\n🔧 Installing {description}...")
        print(f"📝 Note: {note}")
        
        if run_command(f"pip install {package}", f"Installing {description}"):
            success_count += 1
        else:
            print(f"⚠️  {description} will not be available")
            print(f"💡 You may need to install system dependencies first")
    
    # Special handling for camelot-py
    print(f"\n🔧 Installing PDF table extraction (camelot-py)...")
    print(f"📝 Note: Requires OpenCV and other system libraries")
    
    if run_command("pip install camelot-py[cv]==0.11.0", "Installing camelot-py"):
        success_count += 1
    else:
        print(f"⚠️  Advanced PDF table extraction will not be available")
        print(f"💡 Try: pip install camelot-py[base] for basic functionality")
    
    print(f"\n📊 Advanced packages: {success_count}/{len(advanced_packages) + 1} installed successfully")
    return success_count > 0

def check_system_requirements():
    """Check for system requirements."""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8+ is required")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check pip
    try:
        subprocess.run(["pip", "--version"], check=True, capture_output=True)
        print("✅ pip is available")
    except subprocess.CalledProcessError:
        print("❌ pip is not available")
        return False
    
    return True

def main():
    """Main installation process."""
    print("🚀 PDF Processing Platform - Dependency Installation")
    print("=" * 60)
    
    # Check system requirements
    if not check_system_requirements():
        print("\n❌ System requirements not met. Please fix the issues above.")
        sys.exit(1)
    
    # Install core dependencies
    if not install_core_dependencies():
        print("\n❌ Failed to install core dependencies. Please check the errors above.")
        sys.exit(1)
    
    # Install optional dependencies
    install_optional_dependencies()
    
    # Install advanced dependencies
    install_advanced_dependencies()
    
    print("\n" + "=" * 60)
    print("🎉 Installation completed!")
    print("\n📋 Next steps:")
    print("1. Copy .env.example to .env and configure your settings")
    print("2. Run: python run.py")
    print("3. Visit: http://localhost:8000/docs for API documentation")
    
    print("\n💡 Tips:")
    print("- Some advanced features may require additional system libraries")
    print("- Check the logs if any tools don't work as expected")
    print("- Install wkhtmltopdf for HTML to PDF conversion")
    print("- Install Java Runtime for advanced PDF table extraction")

if __name__ == "__main__":
    main()
