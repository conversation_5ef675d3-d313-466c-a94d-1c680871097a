"""
PDF to Excel conversion tool for extracting tables and data from PDFs.
"""
import os
import tempfile
from typing import List, Dict, Any, Optional
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFToExcelTool(BasePDFTool):
    """Tool for converting PDF files to Excel format."""
    
    def __init__(self):
        super().__init__("pdf_to_excel")
        
        # Output formats
        self.output_formats = {
            "xlsx": "Excel Workbook (Office 2007+)",
            "xls": "Excel Workbook (Legacy)",
            "csv": "Comma-Separated Values"
        }
        
        # Extraction methods
        self.extraction_methods = {
            "tables": "Extract tables only",
            "text": "Extract all text as data",
            "mixed": "Extract both tables and text",
            "auto": "Automatically detect best method"
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Convert PDF files to Excel format.
        
        Args:
            input_files: List of PDF file paths to convert
            output_path: Output directory for Excel files
            parameters: Conversion parameters (format, method, etc.)
            
        Returns:
            List containing paths to converted Excel files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for conversion")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        output_format = params.get("output_format", "xlsx")
        extraction_method = params.get("extraction_method", "auto")
        pages = params.get("pages", "all")
        include_headers = params.get("include_headers", True)
        
        # Validate parameters
        if output_format not in self.output_formats:
            raise ValidationError(f"Invalid output format: {output_format}. Available: {list(self.output_formats.keys())}")
        
        if extraction_method not in self.extraction_methods:
            raise ValidationError(f"Invalid extraction method: {extraction_method}. Available: {list(self.extraction_methods.keys())}")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF to Excel conversion",
                input_count=len(input_files),
                output_format=output_format,
                extraction_method=extraction_method,
                pages=pages
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Converting file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}.{output_format}"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Convert the PDF
                    conversion_result = await self._convert_pdf_to_excel(
                        input_file,
                        output_file,
                        output_format,
                        extraction_method,
                        pages,
                        include_headers
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create Excel file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} converted successfully",
                        input_file=input_file,
                        output_file=output_file,
                        tables_extracted=conversion_result["tables_extracted"],
                        pages_processed=conversion_result["pages_processed"],
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to convert file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to convert {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PDF to Excel conversion completed successfully",
                input_files=len(input_files),
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF to Excel conversion: {str(e)}")
    
    async def _convert_pdf_to_excel(
        self,
        input_file: str,
        output_file: str,
        output_format: str,
        extraction_method: str,
        pages: str,
        include_headers: bool
    ) -> Dict[str, Any]:
        """Convert a single PDF file to Excel."""
        try:
            # Try different extraction methods based on availability
            
            # Method 1: Try tabula-py for table extraction
            try:
                return await self._extract_with_tabula(
                    input_file, output_file, output_format, pages, include_headers
                )
            except Exception as e:
                self.logger.warning(f"Tabula extraction failed: {str(e)}")
            
            # Method 2: Try camelot for table extraction
            try:
                return await self._extract_with_camelot(
                    input_file, output_file, output_format, pages, include_headers
                )
            except Exception as e:
                self.logger.warning(f"Camelot extraction failed: {str(e)}")
            
            # Method 3: Try pdfplumber for text-based extraction
            try:
                return await self._extract_with_pdfplumber(
                    input_file, output_file, output_format, extraction_method, pages, include_headers
                )
            except Exception as e:
                self.logger.warning(f"pdfplumber extraction failed: {str(e)}")
            
            # Method 4: Fallback to PyMuPDF text extraction
            return await self._extract_with_pymupdf(
                input_file, output_file, output_format, pages, include_headers
            )
            
        except Exception as e:
            raise ProcessingError(f"Failed to convert PDF to Excel: {str(e)}")
    
    async def _extract_with_tabula(
        self,
        input_file: str,
        output_file: str,
        output_format: str,
        pages: str,
        include_headers: bool
    ) -> Dict[str, Any]:
        """Extract tables using tabula-py."""
        try:
            import tabula
            import pandas as pd
            
            # Parse pages parameter
            if pages == "all":
                page_param = "all"
            else:
                # Convert page specification to tabula format
                page_param = pages
            
            # Extract tables
            tables = tabula.read_pdf(
                input_file,
                pages=page_param,
                multiple_tables=True,
                pandas_options={'header': 0 if include_headers else None}
            )
            
            if not tables:
                raise ProcessingError("No tables found in PDF")
            
            # Save to Excel
            if output_format == "csv":
                # For CSV, save the first table or combine all
                if len(tables) == 1:
                    tables[0].to_csv(output_file, index=False)
                else:
                    # Combine all tables
                    combined_df = pd.concat(tables, ignore_index=True)
                    combined_df.to_csv(output_file, index=False)
            else:
                # For Excel formats
                with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                    for i, table in enumerate(tables):
                        sheet_name = f"Table_{i+1}" if len(tables) > 1 else "Sheet1"
                        table.to_excel(writer, sheet_name=sheet_name, index=False)
            
            return {
                "tables_extracted": len(tables),
                "pages_processed": len(str(page_param).split(',')),
                "extraction_method": "tabula"
            }
            
        except ImportError:
            raise ProcessingError("tabula-py is not installed. Install with: pip install tabula-py")
        except Exception as e:
            raise ProcessingError(f"Tabula extraction failed: {str(e)}")
    
    async def _extract_with_camelot(
        self,
        input_file: str,
        output_file: str,
        output_format: str,
        pages: str,
        include_headers: bool
    ) -> Dict[str, Any]:
        """Extract tables using camelot."""
        try:
            import camelot
            import pandas as pd
            
            # Parse pages parameter
            if pages == "all":
                page_param = "1-end"
            else:
                page_param = pages
            
            # Extract tables
            tables = camelot.read_pdf(input_file, pages=page_param)
            
            if not tables:
                raise ProcessingError("No tables found in PDF")
            
            # Convert to pandas DataFrames
            dataframes = [table.df for table in tables]
            
            # Save to Excel
            if output_format == "csv":
                if len(dataframes) == 1:
                    dataframes[0].to_csv(output_file, index=False, header=include_headers)
                else:
                    combined_df = pd.concat(dataframes, ignore_index=True)
                    combined_df.to_csv(output_file, index=False, header=include_headers)
            else:
                with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                    for i, df in enumerate(dataframes):
                        sheet_name = f"Table_{i+1}" if len(dataframes) > 1 else "Sheet1"
                        df.to_excel(writer, sheet_name=sheet_name, index=False, header=include_headers)
            
            return {
                "tables_extracted": len(tables),
                "pages_processed": len(page_param.split(',')),
                "extraction_method": "camelot"
            }
            
        except ImportError:
            raise ProcessingError("camelot-py is not installed. Install with: pip install camelot-py[cv]")
        except Exception as e:
            raise ProcessingError(f"Camelot extraction failed: {str(e)}")
    
    async def _extract_with_pdfplumber(
        self,
        input_file: str,
        output_file: str,
        output_format: str,
        extraction_method: str,
        pages: str,
        include_headers: bool
    ) -> Dict[str, Any]:
        """Extract data using pdfplumber."""
        try:
            import pdfplumber
            import pandas as pd
            
            all_tables = []
            pages_processed = 0
            
            with pdfplumber.open(input_file) as pdf:
                # Determine which pages to process
                if pages == "all":
                    page_numbers = range(len(pdf.pages))
                else:
                    # Parse specific pages
                    page_numbers = self._parse_page_numbers(pages, len(pdf.pages))
                
                for page_num in page_numbers:
                    if page_num >= len(pdf.pages):
                        continue
                    
                    page = pdf.pages[page_num]
                    pages_processed += 1
                    
                    if extraction_method in ["tables", "auto"]:
                        # Try to extract tables
                        tables = page.extract_tables()
                        if tables:
                            for table in tables:
                                if table:  # Skip empty tables
                                    df = pd.DataFrame(table)
                                    if include_headers and len(df) > 0:
                                        df.columns = df.iloc[0]
                                        df = df.drop(df.index[0])
                                    all_tables.append(df)
                    
                    if extraction_method in ["text", "mixed"] or (extraction_method == "auto" and not all_tables):
                        # Extract text and try to structure it
                        text = page.extract_text()
                        if text:
                            lines = text.split('\n')
                            # Simple heuristic: treat each line as a row
                            text_data = []
                            for line in lines:
                                if line.strip():
                                    # Split by multiple spaces or tabs
                                    row = [cell.strip() for cell in line.split() if cell.strip()]
                                    if row:
                                        text_data.append(row)
                            
                            if text_data:
                                # Pad rows to same length
                                max_cols = max(len(row) for row in text_data)
                                for row in text_data:
                                    while len(row) < max_cols:
                                        row.append("")
                                
                                df = pd.DataFrame(text_data)
                                if include_headers and len(df) > 0:
                                    df.columns = df.iloc[0]
                                    df = df.drop(df.index[0])
                                all_tables.append(df)
            
            if not all_tables:
                raise ProcessingError("No data could be extracted from PDF")
            
            # Save to Excel
            if output_format == "csv":
                if len(all_tables) == 1:
                    all_tables[0].to_csv(output_file, index=False)
                else:
                    combined_df = pd.concat(all_tables, ignore_index=True)
                    combined_df.to_csv(output_file, index=False)
            else:
                with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                    for i, df in enumerate(all_tables):
                        sheet_name = f"Page_{i+1}" if len(all_tables) > 1 else "Sheet1"
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            return {
                "tables_extracted": len(all_tables),
                "pages_processed": pages_processed,
                "extraction_method": "pdfplumber"
            }
            
        except ImportError:
            raise ProcessingError("pdfplumber is not installed. Install with: pip install pdfplumber")
        except Exception as e:
            raise ProcessingError(f"pdfplumber extraction failed: {str(e)}")
    
    async def _extract_with_pymupdf(
        self,
        input_file: str,
        output_file: str,
        output_format: str,
        pages: str,
        include_headers: bool
    ) -> Dict[str, Any]:
        """Fallback extraction using PyMuPDF."""
        try:
            import fitz
            import pandas as pd
            
            doc = fitz.open(input_file)
            
            if doc.needs_pass:
                doc.close()
                raise ProcessingError("Cannot extract from password-protected PDF")
            
            all_data = []
            pages_processed = 0
            
            # Determine which pages to process
            if pages == "all":
                page_numbers = range(len(doc))
            else:
                page_numbers = self._parse_page_numbers(pages, len(doc))
            
            for page_num in page_numbers:
                if page_num >= len(doc):
                    continue
                
                page = doc[page_num]
                text = page.get_text()
                pages_processed += 1
                
                if text.strip():
                    lines = text.split('\n')
                    for line in lines:
                        if line.strip():
                            # Simple text parsing - split by whitespace
                            row = [cell.strip() for cell in line.split() if cell.strip()]
                            if row:
                                all_data.append(row)
            
            doc.close()
            
            if not all_data:
                raise ProcessingError("No text data could be extracted from PDF")
            
            # Pad rows to same length
            max_cols = max(len(row) for row in all_data)
            for row in all_data:
                while len(row) < max_cols:
                    row.append("")
            
            df = pd.DataFrame(all_data)
            
            if include_headers and len(df) > 0:
                df.columns = df.iloc[0]
                df = df.drop(df.index[0])
            
            # Save to file
            if output_format == "csv":
                df.to_csv(output_file, index=False)
            else:
                df.to_excel(output_file, index=False)
            
            return {
                "tables_extracted": 1,
                "pages_processed": pages_processed,
                "extraction_method": "pymupdf_text"
            }
            
        except Exception as e:
            raise ProcessingError(f"PyMuPDF extraction failed: {str(e)}")
    
    def _parse_page_numbers(self, pages: str, total_pages: int) -> List[int]:
        """Parse page numbers from string specification."""
        page_numbers = []
        
        for part in pages.split(','):
            part = part.strip()
            
            if '-' in part:
                start_str, end_str = part.split('-', 1)
                start = int(start_str.strip()) - 1  # Convert to 0-based
                end = int(end_str.strip()) - 1
                page_numbers.extend(range(start, end + 1))
            else:
                page_numbers.append(int(part.strip()) - 1)  # Convert to 0-based
        
        return [p for p in page_numbers if 0 <= p < total_pages]
    
    def get_conversion_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available conversion options."""
        return {
            "output_format": {
                "description": "Output file format",
                "type": "string",
                "options": list(self.output_formats.keys()),
                "default": "xlsx",
                "format_descriptions": self.output_formats
            },
            "extraction_method": {
                "description": "Data extraction method",
                "type": "string",
                "options": list(self.extraction_methods.keys()),
                "default": "auto",
                "method_descriptions": self.extraction_methods
            },
            "pages": {
                "description": "Pages to extract data from",
                "type": "string",
                "default": "all",
                "example": "1,3,5-7"
            },
            "include_headers": {
                "description": "Treat first row as headers",
                "type": "boolean",
                "default": True
            }
        }
    
    def get_output_formats(self) -> Dict[str, str]:
        """Get supported output formats."""
        return self.output_formats.copy()
    
    def get_extraction_methods(self) -> Dict[str, str]:
        """Get available extraction methods."""
        return self.extraction_methods.copy()


# Create tool instance
pdf_to_excel_tool = PDFToExcelTool()
