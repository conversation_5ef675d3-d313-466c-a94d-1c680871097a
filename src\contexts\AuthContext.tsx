import React, { createContext, useContext, useState, useEffect } from 'react';
import { authApi, AuthResponse, UserProfile } from '../services/authApi';

export interface User {
  id: string;
  email: string;
  name: string;
  plan: 'free' | 'premium';
  dailyUsage: number;
  lastUsageDate: string;
  createdAt: string;
  premiumExpiresAt?: string;
}



interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  signup: (email: string, password: string, name: string) => Promise<boolean>;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
  getRemainingUsage: () => number;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Convert backend user format to frontend format
  const convertBackendUser = (backendUser: UserProfile): User => ({
    id: backendUser.id.toString(),
    email: backendUser.email,
    name: backendUser.name,
    plan: backendUser.plan as 'free' | 'premium',
    dailyUsage: backendUser.daily_usage || 0,
    lastUsageDate: backendUser.last_usage_date || new Date().toDateString(),
    createdAt: backendUser.created_at,
    premiumExpiresAt: backendUser.premium_expires_at,
  });

  // Load user from token on mount
  useEffect(() => {
    const initializeAuth = async () => {
      if (authApi.isAuthenticated()) {
        try {
          const userData = await authApi.getCurrentUser();
          const convertedUser = convertBackendUser(userData);
          setUser(convertedUser);
          localStorage.setItem('currentUser', JSON.stringify(convertedUser));
        } catch (error) {
          console.error('Error loading user from token:', error);
          authApi.clearAuth();
        }
      }
      setIsLoading(false);
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const response = await authApi.login({ email, password });

      // Store token
      authApi.setToken(response.access_token);

      // Convert and store user
      const convertedUser = convertBackendUser(response.user);
      setUser(convertedUser);
      localStorage.setItem('currentUser', JSON.stringify(convertedUser));

      return true;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  };

  const signup = async (email: string, password: string, name: string): Promise<boolean> => {
    try {
      const response = await authApi.register({ email, password, name });

      // Store token
      authApi.setToken(response.access_token);

      // Convert and store user
      const convertedUser = convertBackendUser(response.user);
      setUser(convertedUser);
      localStorage.setItem('currentUser', JSON.stringify(convertedUser));

      return true;
    } catch (error) {
      console.error('Signup failed:', error);
      return false;
    }
  };

  const logout = async () => {
    await authApi.logout();
    setUser(null);
  };

  const updateUser = async (updates: Partial<User>) => {
    if (!user) return;

    try {
      // Convert frontend updates to backend format
      const backendUpdates: Partial<UserProfile> = {
        ...updates,
        id: updates.id ? parseInt(updates.id) : undefined,
        daily_usage: updates.dailyUsage,
        last_usage_date: updates.lastUsageDate,
        created_at: updates.createdAt,
        premium_expires_at: updates.premiumExpiresAt,
      };

      // Remove undefined values and frontend-specific fields
      Object.keys(backendUpdates).forEach(key => {
        if (backendUpdates[key as keyof UserProfile] === undefined ||
            ['dailyUsage', 'lastUsageDate', 'createdAt', 'premiumExpiresAt'].includes(key)) {
          delete backendUpdates[key as keyof UserProfile];
        }
      });

      // Update user on backend
      const updatedBackendUser = await authApi.updateProfile(backendUpdates);

      // Update local state
      const convertedUser = convertBackendUser(updatedBackendUser);
      setUser(convertedUser);
      localStorage.setItem('currentUser', JSON.stringify(convertedUser));
    } catch (error) {
      console.error('Failed to update user:', error);
    }
  };

  const refreshUser = async () => {
    if (!authApi.isAuthenticated()) return;

    try {
      const userData = await authApi.getCurrentUser();
      const convertedUser = convertBackendUser(userData);
      setUser(convertedUser);
      localStorage.setItem('currentUser', JSON.stringify(convertedUser));
    } catch (error) {
      console.error('Failed to refresh user:', error);
      await logout();
    }
  };


  const getRemainingUsage = (): number => {
    if (!user) return 0;
    if (user.plan === 'premium') return 999;
    return Math.max(0, 3 - user.dailyUsage);
  };

  const isAuthenticated = !!user;

  return (
    <AuthContext.Provider value={{
      user,
      isAuthenticated,
      isLoading,
      login,
      signup,
      logout,
      updateUser,
      getRemainingUsage,
      refreshUser
    }}>
      {children}
    </AuthContext.Provider>
  );
};