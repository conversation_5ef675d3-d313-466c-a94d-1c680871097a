"""
PDF compression tool for reducing file size.
"""
import os
import fitz  # PyMuPDF
from typing import List, Dict, Any, Optional
import structlog

from .base_tool import Base<PERSON><PERSON><PERSON>, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFCompressTool(BasePDFTool):
    """Tool for compressing PDF files to reduce file size."""
    
    def __init__(self):
        super().__init__("compress")
        
        # Compression level settings
        self.compression_settings = {
            "low": {
                "image_quality": 85,
                "image_dpi": 150,
                "deflate_level": 1,
                "description": "Light compression, high quality"
            },
            "medium": {
                "image_quality": 70,
                "image_dpi": 120,
                "deflate_level": 6,
                "description": "Balanced compression and quality"
            },
            "high": {
                "image_quality": 50,
                "image_dpi": 96,
                "deflate_level": 9,
                "description": "Maximum compression, lower quality"
            }
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Compress PDF files to reduce file size.
        
        Args:
            input_files: List of PDF file paths to compress
            output_path: Output directory or file path
            parameters: Compression parameters (level, quality, etc.)
            
        Returns:
            List containing paths to compressed PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for compression")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        compression_level = params.get("compression_level", "medium")
        image_quality = params.get("image_quality", None)
        remove_metadata = params.get("remove_metadata", True)

        if compression_level not in self.compression_settings:
            raise ValidationError(f"Invalid compression level: {compression_level}")

        settings = self.compression_settings[compression_level].copy()

        # Override image quality if provided
        if image_quality is not None:
            if not (10 <= image_quality <= 100):
                raise ValidationError("Image quality must be between 10 and 100")
            settings["image_quality"] = image_quality
        
        # Ensure output directory exists
        if os.path.isdir(output_path):
            output_dir = output_path
        else:
            output_dir = os.path.dirname(output_path)
        
        self.ensure_output_directory(output_dir)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF compression",
                input_count=len(input_files),
                compression_level=compression_level,
                settings=settings
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Compressing file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    if len(input_files) == 1 and not os.path.isdir(output_path):
                        output_file = output_path
                    else:
                        output_filename = self.get_output_filename(input_file, "compressed")
                        output_file = os.path.join(output_dir, output_filename)
                    
                    # Get original file size
                    original_size = self.get_file_size_mb(input_file)
                    
                    # Compress the PDF
                    compressed_size = await self._compress_pdf(
                        input_file,
                        output_file,
                        settings,
                        remove_metadata
                    )
                    
                    # Calculate compression ratio
                    compression_ratio = (1 - compressed_size / original_size) * 100 if original_size > 0 else 0
                    
                    self.logger.info(
                        f"File {i+1} compressed successfully",
                        input_file=input_file,
                        output_file=output_file,
                        original_size_mb=round(original_size, 2),
                        compressed_size_mb=round(compressed_size, 2),
                        compression_ratio=round(compression_ratio, 1)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to compress file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to compress {os.path.basename(input_file)}: {str(e)}")
            
            total_original_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_compressed_size = sum(self.get_file_size_mb(f) for f in output_files)
            overall_compression = (1 - total_compressed_size / total_original_size) * 100 if total_original_size > 0 else 0
            
            self.logger.info(
                "PDF compression completed successfully",
                output_files=len(output_files),
                total_original_size_mb=round(total_original_size, 2),
                total_compressed_size_mb=round(total_compressed_size, 2),
                overall_compression_ratio=round(overall_compression, 1)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF compression: {str(e)}")
    
    async def _compress_pdf(self, input_file: str, output_file: str, settings: Dict[str, Any], remove_metadata: bool = True) -> float:
        """Compress a single PDF file using PyMuPDF."""
        try:
            # Open the PDF document
            doc = fitz.open(input_file)
            
            # Check if document is encrypted
            if doc.needs_pass:
                doc.close()
                raise ProcessingError("Cannot compress password-protected PDF")
            
            # Create a new document for the compressed version
            compressed_doc = fitz.open()
            
            page_count = len(doc)
            
            for page_num in range(page_count):
                page = doc[page_num]
                
                # Get page as pixmap with reduced DPI for compression
                mat = fitz.Matrix(settings["image_dpi"] / 72, settings["image_dpi"] / 72)
                pix = page.get_pixmap(matrix=mat)
                
                # Convert pixmap to image bytes with compression
                img_data = pix.tobytes("jpeg", jpg_quality=settings["image_quality"])
                
                # Create new page in compressed document
                new_page = compressed_doc.new_page(width=page.rect.width, height=page.rect.height)
                
                # Insert compressed image
                img_rect = fitz.Rect(0, 0, page.rect.width, page.rect.height)
                new_page.insert_image(img_rect, stream=img_data)
                
                # Copy text content if possible (to maintain searchability)
                try:
                    text_dict = page.get_text("dict")
                    if text_dict and text_dict.get("blocks"):
                        # This is a simplified text preservation - in production you might want more sophisticated text handling
                        pass
                except Exception:
                    # If text extraction fails, continue without text
                    pass
            
            # Handle metadata removal
            if not remove_metadata and doc.metadata:
                # Preserve original metadata
                compressed_doc.set_metadata(doc.metadata)

            # Save with compression options
            compressed_doc.save(
                output_file,
                deflate=True,
                deflate_level=settings["deflate_level"],
                clean=True,
                garbage=4
            )
            
            # Close documents
            doc.close()
            compressed_doc.close()
            
            # Return compressed file size
            return self.get_file_size_mb(output_file)
            
        except Exception as e:
            raise ProcessingError(f"Failed to compress PDF: {str(e)}")
    
    def get_compression_info(self, level: str) -> Dict[str, Any]:
        """Get information about a compression level."""
        if level not in self.compression_settings:
            raise ValidationError(f"Invalid compression level: {level}")
        
        return self.compression_settings[level].copy()
    
    def get_available_levels(self) -> List[str]:
        """Get list of available compression levels."""
        return list(self.compression_settings.keys())
    
    async def estimate_compression(self, file_path: str, level: str) -> Dict[str, Any]:
        """Estimate compression results without actually compressing."""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        if level not in self.compression_settings:
            raise ValidationError(f"Invalid compression level: {level}")
        
        original_size = self.get_file_size_mb(file_path)
        
        # Rough estimation based on compression level
        # These are approximate values based on typical PDF compression results
        estimated_ratios = {
            "low": 0.15,      # ~15% compression
            "medium": 0.35,   # ~35% compression  
            "high": 0.60      # ~60% compression
        }
        
        estimated_compression = estimated_ratios[level]
        estimated_size = original_size * (1 - estimated_compression)
        
        return {
            "original_size_mb": round(original_size, 2),
            "estimated_size_mb": round(estimated_size, 2),
            "estimated_compression_ratio": round(estimated_compression * 100, 1),
            "compression_level": level,
            "settings": self.compression_settings[level]
        }


# Create tool instance
compress_tool = PDFCompressTool()
