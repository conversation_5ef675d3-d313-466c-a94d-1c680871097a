"""
PDF digital signature tool for adding digital signatures to PDFs.
"""
import os
import tempfile
from typing import List, Dict, Any, Optional
from datetime import datetime
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFSignTool(BasePDFTool):
    """Tool for adding digital signatures to PDF files."""
    
    def __init__(self):
        super().__init__("sign")
        
        # Signature types
        self.signature_types = {
            "text": "Text-based signature",
            "image": "Image signature",
            "digital": "Digital certificate signature",
            "timestamp": "Timestamp signature"
        }
        
        # Signature positions
        self.positions = {
            "bottom-left": (0.1, 0.1),
            "bottom-center": (0.5, 0.1),
            "bottom-right": (0.9, 0.1),
            "top-left": (0.1, 0.9),
            "top-center": (0.5, 0.9),
            "top-right": (0.9, 0.9),
            "center": (0.5, 0.5)
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Add digital signatures to PDF files.
        
        Args:
            input_files: List of PDF file paths to sign
            output_path: Output directory for signed PDFs
            parameters: Signature parameters (type, position, content, etc.)
            
        Returns:
            List containing paths to signed PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for signing")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        signature_type = params.get("signature_type", "text")
        signature_text = params.get("signature_text", "Digitally Signed")
        position = params.get("position", "bottom-right")
        pages = params.get("pages", "last")  # "all", "first", "last", or specific pages
        signer_name = params.get("signer_name", "")
        reason = params.get("reason", "Document approval")
        
        # Validate parameters
        if signature_type not in self.signature_types:
            raise ValidationError(f"Invalid signature type: {signature_type}. Available: {list(self.signature_types.keys())}")
        
        if position not in self.positions:
            raise ValidationError(f"Invalid position: {position}. Available: {list(self.positions.keys())}")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF signing",
                input_count=len(input_files),
                signature_type=signature_type,
                position=position,
                pages=pages
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Signing file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}_signed.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Sign the PDF
                    signing_result = await self._sign_pdf(
                        input_file,
                        output_file,
                        signature_type,
                        signature_text,
                        position,
                        pages,
                        signer_name,
                        reason
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create signed PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} signed successfully",
                        input_file=input_file,
                        output_file=output_file,
                        signatures_added=signing_result["signatures_added"],
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to sign file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to sign {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PDF signing completed successfully",
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF signing: {str(e)}")
    
    async def _sign_pdf(
        self,
        input_file: str,
        output_file: str,
        signature_type: str,
        signature_text: str,
        position: str,
        pages: str,
        signer_name: str,
        reason: str
    ) -> Dict[str, Any]:
        """Add signature to a PDF file."""
        try:
            # Try different signing methods based on availability and type
            
            if signature_type == "digital":
                # Try digital certificate signing
                try:
                    return await self._sign_with_certificate(
                        input_file, output_file, signer_name, reason
                    )
                except Exception as e:
                    self.logger.warning(f"Digital certificate signing failed: {str(e)}")
                    # Fallback to text signature
                    signature_type = "text"
            
            # Use PyMuPDF for text/visual signatures
            return await self._sign_with_pymupdf(
                input_file, output_file, signature_type, signature_text,
                position, pages, signer_name, reason
            )
            
        except Exception as e:
            raise ProcessingError(f"Failed to sign PDF: {str(e)}")
    
    async def _sign_with_pymupdf(
        self,
        input_file: str,
        output_file: str,
        signature_type: str,
        signature_text: str,
        position: str,
        pages: str,
        signer_name: str,
        reason: str
    ) -> Dict[str, Any]:
        """Sign PDF using PyMuPDF for visual signatures."""
        try:
            import fitz
            
            # Open PDF
            doc = fitz.open(input_file)
            
            if doc.needs_pass:
                doc.close()
                raise ProcessingError("Cannot sign password-protected PDF")
            
            total_pages = len(doc)
            
            # Determine which pages to sign
            pages_to_sign = self._parse_pages_parameter(pages, total_pages)
            
            signatures_added = 0
            
            # Add signature to specified pages
            for page_num in range(total_pages):
                if (page_num + 1) not in pages_to_sign:
                    continue
                
                page = doc[page_num]
                
                # Get page dimensions
                page_rect = page.rect
                page_width = page_rect.width
                page_height = page_rect.height
                
                # Calculate signature position
                pos_x, pos_y = self.positions[position]
                x = page_width * pos_x
                y = page_height * pos_y
                
                # Create signature content
                if signature_type == "text":
                    signature_content = self._create_text_signature(
                        signature_text, signer_name, reason
                    )
                elif signature_type == "timestamp":
                    signature_content = self._create_timestamp_signature(signer_name)
                else:
                    signature_content = signature_text
                
                # Add signature annotation
                signature_rect = fitz.Rect(x - 100, y - 30, x + 100, y + 30)
                
                # Create text annotation for signature
                annot = page.add_text_annot(fitz.Point(x, y), signature_content)
                annot.set_info(content=signature_content)
                annot.update()
                
                # Add visual signature box
                page.draw_rect(signature_rect, color=(0, 0, 1), width=1)
                
                # Add signature text
                page.insert_text(
                    fitz.Point(x - 95, y - 10),
                    signature_content,
                    fontsize=8,
                    color=(0, 0, 1)
                )
                
                signatures_added += 1
                
                self.logger.debug(f"Added signature to page {page_num + 1}")
            
            # Save signed PDF
            doc.save(output_file, garbage=4, deflate=True, clean=True)
            doc.close()
            
            return {
                "signatures_added": signatures_added,
                "signature_type": signature_type,
                "pages_signed": list(pages_to_sign)
            }
            
        except Exception as e:
            raise ProcessingError(f"Failed to sign with PyMuPDF: {str(e)}")
    
    async def _sign_with_certificate(
        self,
        input_file: str,
        output_file: str,
        signer_name: str,
        reason: str
    ) -> Dict[str, Any]:
        """Sign PDF with digital certificate (placeholder implementation)."""
        try:
            # This is a placeholder for digital certificate signing
            # In a real implementation, you would use libraries like:
            # - cryptography for certificate handling
            # - PyPDF2 or similar for PDF signing
            # - PKCS#11 for hardware security modules
            
            # For now, we'll create a simple signed copy with metadata
            import shutil
            from PyPDF2 import PdfWriter, PdfReader
            
            with open(input_file, 'rb') as infile:
                reader = PdfReader(infile)
                writer = PdfWriter()
                
                # Copy all pages
                for page in reader.pages:
                    writer.add_page(page)
                
                # Add signature metadata
                metadata = reader.metadata or {}
                metadata.update({
                    "/Producer": "PDF Signature Tool",
                    "/Creator": f"Signed by {signer_name}",
                    "/Subject": reason,
                    "/ModDate": datetime.now().strftime("D:%Y%m%d%H%M%S"),
                    "/Signature": f"Digital signature by {signer_name}"
                })
                
                writer.add_metadata(metadata)
                
                # Write signed PDF
                with open(output_file, 'wb') as outfile:
                    writer.write(outfile)
            
            return {
                "signatures_added": 1,
                "signature_type": "digital",
                "signer": signer_name,
                "reason": reason,
                "note": "Basic digital signature metadata added. For full PKI signatures, additional certificate infrastructure is required."
            }
            
        except Exception as e:
            raise ProcessingError(f"Failed to sign with certificate: {str(e)}")
    
    def _create_text_signature(self, signature_text: str, signer_name: str, reason: str) -> str:
        """Create formatted text signature."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        signature_lines = []
        if signature_text:
            signature_lines.append(signature_text)
        if signer_name:
            signature_lines.append(f"Signed by: {signer_name}")
        if reason:
            signature_lines.append(f"Reason: {reason}")
        signature_lines.append(f"Date: {timestamp}")
        
        return "\n".join(signature_lines)
    
    def _create_timestamp_signature(self, signer_name: str) -> str:
        """Create timestamp-based signature."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
        return f"Timestamp: {timestamp}\nSigned by: {signer_name or 'System'}"
    
    def _parse_pages_parameter(self, pages: str, total_pages: int) -> set:
        """Parse the pages parameter to get set of page numbers to sign."""
        if pages == "all":
            return set(range(1, total_pages + 1))
        elif pages == "first":
            return {1} if total_pages > 0 else set()
        elif pages == "last":
            return {total_pages} if total_pages > 0 else set()
        else:
            # Parse specific pages (e.g., "1,3,5-7,10")
            page_numbers = set()
            
            for part in pages.split(','):
                part = part.strip()
                
                if '-' in part:
                    # Range like "5-7"
                    start_str, end_str = part.split('-', 1)
                    start = int(start_str.strip())
                    end = int(end_str.strip())
                    
                    if start < 1 or end > total_pages or start > end:
                        raise ValidationError(f"Invalid page range: {part}")
                    
                    page_numbers.update(range(start, end + 1))
                else:
                    # Single page like "1"
                    page = int(part)
                    
                    if page < 1 or page > total_pages:
                        raise ValidationError(f"Invalid page number: {page}")
                    
                    page_numbers.add(page)
            
            return page_numbers
    
    def get_signature_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available signature options."""
        return {
            "signature_type": {
                "description": "Type of signature to add",
                "type": "string",
                "options": list(self.signature_types.keys()),
                "default": "text",
                "type_descriptions": self.signature_types
            },
            "signature_text": {
                "description": "Text content for signature",
                "type": "string",
                "default": "Digitally Signed",
                "example": "Approved by John Doe"
            },
            "position": {
                "description": "Position of signature on page",
                "type": "string",
                "options": list(self.positions.keys()),
                "default": "bottom-right"
            },
            "pages": {
                "description": "Pages to sign",
                "type": "string",
                "options": ["all", "first", "last", "specific (e.g., 1,3,5-7)"],
                "default": "last"
            },
            "signer_name": {
                "description": "Name of the person signing",
                "type": "string",
                "default": "",
                "example": "John Doe"
            },
            "reason": {
                "description": "Reason for signing",
                "type": "string",
                "default": "Document approval",
                "example": "Contract approval"
            }
        }
    
    def get_signature_types(self) -> Dict[str, str]:
        """Get available signature types."""
        return self.signature_types.copy()
    
    def get_available_positions(self) -> List[str]:
        """Get available signature positions."""
        return list(self.positions.keys())


# Create tool instance
sign_pdf_tool = PDFSignTool()
