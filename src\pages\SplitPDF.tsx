import React, { useState } from 'react';
import { Split, Download, ArrowRight, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import PDFToolWrapper, { usePDFToolErrorHandler } from '../components/PDFToolWrapper';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const SplitPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [splitMethod, setSplitMethod] = useState<'pages' | 'ranges' | 'size' | 'bookmarks' | 'extract'>('pages');
  const [pagesPerSplit, setPagesPerSplit] = useState(1);
  const [pageRanges, setPageRanges] = useState('');
  const [maxSizeMB, setMaxSizeMB] = useState(10);
  const [extractPages, setExtractPages] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { downloadAllFiles } = useSession();

  const {
    isProcessing,
    progress,
    message,
    error,
    outputFiles,
    taskId,
    splitPDF,
    resetState
  } = usePDFProcessor();

  const {
    handleProcessingError,
    handleDownloadError,
    clearError,
    renderError
  } = usePDFToolErrorHandler();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleSplit = async () => {
    if (files.length === 0) {
      handleProcessingError('Veuillez sélectionner un fichier PDF à diviser', 'Sélection de fichier');
      return;
    }

    clearError(); // Clear any previous errors

    try {
      const parameters = {
        split_method: splitMethod,
        pages_per_split: splitMethod === 'pages' ? pagesPerSplit : undefined,
        page_ranges: splitMethod === 'ranges' ? pageRanges : undefined,
        max_size_mb: splitMethod === 'size' ? maxSizeMB : undefined,
        extract_pages: splitMethod === 'extract' ? extractPages : undefined,
      };

      await splitPDF(files, parameters, {
        onProgress: (progress, message) => {
          console.log(`Progress: ${progress}% - ${message}`);
        },
        onComplete: (outputFiles) => {
          console.log('Split completed:', outputFiles);
        },
        onError: (error) => {
          handleProcessingError(error, 'Division PDF');
        }
      });
    } catch (err) {
      handleProcessingError(err, 'Division PDF');
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        handleDownloadError(err);
      }
    }
  };

  return (
    <PDFToolWrapper onRetry={handleSplit}>
      <ToolLayout
        title="Diviser PDF"
        description="Sélectionner la portée de pages, séparer une page, ou convertir chaque page du document en fichier PDF indépendant"
        icon={<Split className="w-8 h-8" />}
        color="from-purple-500 to-purple-600"
    >
        <div className="space-y-6">
          {renderError()}

          <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de division
            </h3>
            
            <div className="space-y-4">
              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="splitMethod"
                  value="pages"
                  checked={splitMethod === 'pages'}
                  onChange={(e) => setSplitMethod(e.target.value as any)}
                  className="text-blue-600"
                />
                <span className="text-slate-700">Diviser par nombre de pages</span>
              </label>

              {splitMethod === 'pages' && (
                <div className="ml-6">
                  <label className="block text-sm text-slate-600 mb-1">Pages par fichier:</label>
                  <input
                    type="number"
                    min="1"
                    value={pagesPerSplit}
                    onChange={(e) => setPagesPerSplit(parseInt(e.target.value) || 1)}
                    className="w-32 p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}

              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="splitMethod"
                  value="ranges"
                  checked={splitMethod === 'ranges'}
                  onChange={(e) => setSplitMethod(e.target.value as any)}
                  className="text-blue-600"
                />
                <span className="text-slate-700">Diviser par plages de pages</span>
              </label>

              {splitMethod === 'ranges' && (
                <div className="ml-6">
                  <label className="block text-sm text-slate-600 mb-1">Plages (ex: 1-5,10-15):</label>
                  <input
                    type="text"
                    placeholder="1-5, 10-15, 20-25"
                    value={pageRanges}
                    onChange={(e) => setPageRanges(e.target.value)}
                    className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}

              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="splitMethod"
                  value="extract"
                  checked={splitMethod === 'extract'}
                  onChange={(e) => setSplitMethod(e.target.value as any)}
                  className="text-blue-600"
                />
                <span className="text-slate-700">Extraire des pages spécifiques</span>
              </label>

              {splitMethod === 'extract' && (
                <div className="ml-6">
                  <label className="block text-sm text-slate-600 mb-1">Pages à extraire (ex: 1,3,5):</label>
                  <input
                    type="text"
                    placeholder="1, 3, 5, 7"
                    value={extractPages}
                    onChange={(e) => setExtractPages(e.target.value)}
                    className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}

              {showAdvanced && (
                <>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="splitMethod"
                      value="size"
                      checked={splitMethod === 'size'}
                      onChange={(e) => setSplitMethod(e.target.value as any)}
                      className="text-blue-600"
                    />
                    <span className="text-slate-700">Diviser par taille de fichier</span>
                  </label>

                  {splitMethod === 'size' && (
                    <div className="ml-6">
                      <label className="block text-sm text-slate-600 mb-1">Taille max par fichier (MB):</label>
                      <input
                        type="number"
                        min="1"
                        value={maxSizeMB}
                        onChange={(e) => setMaxSizeMB(parseInt(e.target.value) || 10)}
                        className="w-32 p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  )}

                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="splitMethod"
                      value="bookmarks"
                      checked={splitMethod === 'bookmarks'}
                      onChange={(e) => setSplitMethod(e.target.value as any)}
                      className="text-blue-600"
                    />
                    <span className="text-slate-700">Diviser par signets</span>
                  </label>
                </>
              )}
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-purple-50 border border-purple-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600"></div>
              <span className="text-purple-800 font-medium">Division en cours...</span>
            </div>
            <div className="w-full bg-purple-200 rounded-full h-2 mb-2">
              <div
                className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-purple-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la division</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Division réussie!</h4>
                  <p className="text-sm text-green-700">
                    {outputFiles.length} fichier(s) créé(s)
                  </p>
                </div>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {/* Split Button */}
        {files.length > 0 && !outputFiles && (
          <div className="flex justify-center">
            <button
              onClick={handleSplit}
              disabled={isProcessing}
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Division en cours...</span>
                </>
              ) : (
                <>
                  <Split className="w-5 h-5" />
                  <span>Diviser le PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
        </div>
      </ToolLayout>
    </PDFToolWrapper>
  );
};

export default SplitPDF;