import { useState } from 'react';
import { FileText, Download, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { usePDFProcessor } from '../hooks/usePDFProcessor';
import { useSession } from '../contexts/SessionContext';

const PDFToPowerPoint = () => {
  const { downloadAllFiles } = useSession();
  const {
    isProcessing, progress, message, error, outputFiles, taskId,
    convertPDFToPowerPoint, resetState
  } = usePDFProcessor();
  
  const [files, setFiles] = useState<File[]>([]);
  const [outputFormat, setOutputFormat] = useState<'pptx' | 'ppt'>('pptx');
  const [conversionMethod, setConversionMethod] = useState<'text' | 'image'>('text');
  const [pages, setPages] = useState<string>('');
  const [slideLayout, setSlideLayout] = useState<'standard' | 'widescreen'>('standard');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    resetState();
  };

  const handleConvert = async () => {
    if (files.length === 0) return;
    
    try {
      await convertPDFToPowerPoint(files, {
        output_format: outputFormat,
        conversion_method: conversionMethod,
        pages: pages || undefined,
        slide_layout: slideLayout
      });
    } catch (err) {
      console.error('PDF to PowerPoint conversion failed:', err);
    }
  };

  const handleDownload = async () => {
    if (outputFiles && outputFiles.length > 0 && taskId) {
      try {
        await downloadAllFiles(taskId);
      } catch (err) {
        console.error('Download failed:', err);
      }
    }
  };

  return (
    <ToolLayout
      title="PDF en PowerPoint"
      description="Transformez vos fichiers PDF en présentations PPT et PPTX faciles à éditer"
      icon={<FileText className="w-8 h-8" />}
      color="from-orange-500 to-orange-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de conversion
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Format de sortie
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="format"
                      value="pptx"
                      checked={outputFormat === 'pptx'}
                      onChange={(e) => setOutputFormat(e.target.value as 'pptx')}
                      className="text-orange-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">PPTX (recommandé)</span>
                      <p className="text-sm text-slate-500">Compatible avec PowerPoint 2007 et versions ultérieures</p>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="format"
                      value="ppt"
                      checked={outputFormat === 'ppt'}
                      onChange={(e) => setOutputFormat(e.target.value as 'ppt')}
                      className="text-orange-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">PPT</span>
                      <p className="text-sm text-slate-500">Compatible avec les anciennes versions de PowerPoint</p>
                    </div>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Méthode de conversion
                </label>
                <select
                  value={conversionMethod}
                  onChange={(e) => setConversionMethod(e.target.value as 'text' | 'image')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value="text">Texte éditable (recommandé)</option>
                  <option value="image">Conversion en images</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Disposition des diapositives
                </label>
                <select
                  value={slideLayout}
                  onChange={(e) => setSlideLayout(e.target.value as 'standard' | 'widescreen')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value="standard">Standard (4:3)</option>
                  <option value="widescreen">Écran large (16:9)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Pages à convertir (optionnel)
                </label>
                <input
                  type="text"
                  value={pages}
                  onChange={(e) => setPages(e.target.value)}
                  placeholder="ex: 1-3, 5, 7-9"
                  className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
                <p className="text-sm text-slate-500 mt-1">Laissez vide pour convertir toutes les pages</p>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <div className="bg-orange-50 border border-orange-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3 mb-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"></div>
              <span className="text-orange-800 font-medium">Conversion en cours...</span>
            </div>
            <div className="w-full bg-orange-200 rounded-full h-2 mb-2">
              <div className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                   style={{ width: `${progress}%` }}></div>
            </div>
            <p className="text-sm text-orange-700">{message}</p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="text-red-800 font-medium">Erreur lors de la conversion</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success and Download */}
        {outputFiles && outputFiles.length > 0 && (
          <div className="bg-green-50 border border-green-200 p-4 rounded-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="text-green-800 font-medium">Conversion terminée avec succès!</h4>
                  <p className="text-sm text-green-700">{outputFiles.length} fichier(s) PowerPoint généré(s)</p>
                </div>
              </div>
              <button 
                onClick={handleDownload}
                className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-orange-600 to-red-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en PowerPoint</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default PDFToPowerPoint;