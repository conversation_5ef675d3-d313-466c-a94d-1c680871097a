"""
Database configuration and models.
"""
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Text, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import func
from datetime import datetime
from .config import settings

# Create database engine
engine = create_engine(
    settings.database_url,
    connect_args={"check_same_thread": False} if "sqlite" in settings.database_url else {}
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()


class User(Base):
    """User model for authentication and usage tracking."""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=False)
    plan = Column(String, default="free")  # free, premium
    daily_usage = Column(Integer, default=0)
    last_usage_date = Column(String, default=lambda: datetime.now().strftime("%Y-%m-%d"))
    created_at = Column(DateTime, default=func.now())
    premium_expires_at = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)


class ProcessingSession(Base):
    """Processing session model for tracking user sessions."""
    __tablename__ = "processing_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String, unique=True, index=True, nullable=False)
    user_id = Column(Integer, nullable=True)  # Can be null for anonymous users
    created_at = Column(DateTime, default=func.now())
    last_activity = Column(DateTime, default=func.now())
    is_active = Column(Boolean, default=True)
    temp_dir = Column(String, nullable=False)


class ProcessingTask(Base):
    """Processing task model for tracking individual operations."""
    __tablename__ = "processing_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String, unique=True, index=True, nullable=False)
    session_id = Column(String, nullable=False)
    user_id = Column(Integer, nullable=True)
    tool_name = Column(String, nullable=False)
    status = Column(String, default="pending")  # pending, processing, completed, failed
    input_files = Column(Text)  # JSON string of input file paths
    output_files = Column(Text, nullable=True)  # JSON string of output file paths
    parameters = Column(Text, nullable=True)  # JSON string of processing parameters
    error_message = Column(Text, nullable=True)
    processing_time = Column(Float, nullable=True)  # Processing time in seconds
    created_at = Column(DateTime, default=func.now())
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)


class UsageLog(Base):
    """Usage log model for tracking API usage."""
    __tablename__ = "usage_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=True)
    session_id = Column(String, nullable=False)
    tool_name = Column(String, nullable=False)
    file_size = Column(Integer, nullable=True)  # Size in bytes
    processing_time = Column(Float, nullable=True)  # Processing time in seconds
    success = Column(Boolean, nullable=False)
    timestamp = Column(DateTime, default=func.now())


def get_db():
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """Create all database tables."""
    Base.metadata.create_all(bind=engine)
